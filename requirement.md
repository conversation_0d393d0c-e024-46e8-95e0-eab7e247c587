"Build a fully-functional e-commerce platform backend using Next.js, Supabase for both authentication and database management, and NextAuth.js for additional session management. The platform should include the following components:

Admin Dashboard:

Features for managing users, orders, products, inventory, and payments.

Admin should have full control to add/edit/delete products, view order history, and manage customer data.

Access control should be role-based, with only admin-level access granted to specific users.

Admin should be able to update the platform settings and manage user permissions.

Customer Dashboard:

Customers should be able to view their order history, update profile information, manage their shopping cart, and proceed with checkout.

The dashboard should provide options for tracking orders, saving shipping addresses, and managing payment methods.

Enable customers to reset passwords and manage account details securely.

Authentication:

Implement Supabase Authentication for user login and registration with email/password and social logins (Google, Facebook, etc.).

Include functionality for email verification, password reset, secure session management with JWT (JSON Web Tokens), and multi-factor authentication (if required).

Ensure proper access control and token expiration for users and admins.

Database:

Use Supabase as the backend database to store user data, orders, products, inventory, and transaction records.

Structure the database with relations between tables (users, orders, products, etc.).

Implement role-based access control (RBAC) with Supabase to ensure proper permissions for users and admins.

Security:

Use Supabase security rules for fine-grained control over data access and modification.

Implement HTTPS, secure password storage with bcrypt, and safeguard against SQL injection, XSS, and other vulnerabilities.

Ensure that user sessions are securely managed with JWT and NextAuth.js to handle token-based authentication.

API:

Build RESTful APIs for user registration, login, product management, and order processing.

Ensure that APIs are optimized for performance, with proper error handling and data validation.

Use Supabase API functions for CRUD operations with optimized queries.

Hosting & Deployment:

Host the application on Vercel, with Next.js for frontend rendering and serverless API handling.

Use Supabase for hosting the database and authentication, ensuring that the platform is secure, scalable, and performant.

Real-Time Features (Optional):

Implement real-time inventory updates using Supabase's real-time database to track product stock levels.

Set up notifications for users (e.g., order status updates) and admins (e.g., new orders, low stock).

Bonus:

Implement a basic CMS (Content Management System) for admins to easily update product details, images, and content.

This super prompt should guide Cursor AI in developing the complete backend using Next.js and Supabase, ensuring authentication, database management, and the admin/customer dashboards are set up effectively.