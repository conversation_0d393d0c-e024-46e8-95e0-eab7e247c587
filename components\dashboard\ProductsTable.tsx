import { useState } from 'react';
import { MoreH<PERSON>zontal, Edit, Trash2, Eye, Star, Package } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  compare_price?: number;
  inventory_count: number;
  status: 'active' | 'inactive' | 'out_of_stock';
  featured: boolean;
  category_name?: string;
  images: any[];
  created_at: string;
}

interface ProductsTableProps {
  products: Product[];
  onProductUpdate: () => void;
}

const statusColors = {
  active: 'bg-green-900/20 text-green-400 border-green-500/20',
  inactive: 'bg-gray-900/20 text-gray-400 border-gray-500/20',
  out_of_stock: 'bg-red-900/20 text-red-400 border-red-500/20',
};

export default function ProductsTable({ products, onProductUpdate }: ProductsTableProps) {
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);

  const handleStatusChange = async (productId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        onProductUpdate();
        setActionMenuOpen(null);
      }
    } catch (error) {
      console.error('Failed to update product status:', error);
    }
  };

  const handleFeaturedToggle = async (productId: string, featured: boolean) => {
    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ featured: !featured }),
      });

      if (response.ok) {
        onProductUpdate();
        setActionMenuOpen(null);
      }
    } catch (error) {
      console.error('Failed to update product featured status:', error);
    }
  };

  const handleDelete = async (productId: string) => {
    if (!confirm('Are you sure you want to delete this product?')) return;

    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        onProductUpdate();
        setActionMenuOpen(null);
      }
    } catch (error) {
      console.error('Failed to delete product:', error);
    }
  };

  return (
    <div className="bg-charcoal rounded-lg border border-gold/20 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gold/20 bg-gold/5">
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Product
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Price
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Inventory
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Category
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Created
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gold/20">
            {products.length === 0 ? (
              <tr>
                <td colSpan={7} className="px-6 py-8 text-center text-gray-400">
                  <Package className="w-12 h-12 mx-auto mb-4 text-gray-600" />
                  <p>No products found</p>
                </td>
              </tr>
            ) : (
              products.map((product) => (
                <tr key={product.id} className="hover:bg-gold/5">
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="w-12 h-12 relative mr-4">
                        {product.images?.[0]?.secure_url ? (
                          <Image
                            src={product.images[0].secure_url}
                            alt={product.name}
                            fill
                            className="rounded-lg object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-700 rounded-lg flex items-center justify-center">
                            <Package className="w-6 h-6 text-gray-500" />
                          </div>
                        )}
                      </div>
                      <div>
                        <div className="flex items-center">
                          <div className="text-sm font-medium text-white">
                            {product.name}
                          </div>
                          {product.featured && (
                            <Star className="w-4 h-4 text-gold ml-2" fill="currentColor" />
                          )}
                        </div>
                        <div className="text-sm text-gray-400">{product.slug}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-white font-medium">
                      ${product.price.toFixed(2)}
                    </div>
                    {product.compare_price && (
                      <div className="text-sm text-gray-400 line-through">
                        ${product.compare_price.toFixed(2)}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4">
                    <div className={`text-sm font-medium ${
                      product.inventory_count <= 10 
                        ? 'text-red-400' 
                        : product.inventory_count <= 50 
                        ? 'text-yellow-400' 
                        : 'text-green-400'
                    }`}>
                      {product.inventory_count} units
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium border ${
                      statusColors[product.status]
                    }`}>
                      {product.status.replace('_', ' ')}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-300">
                    {product.category_name || 'Uncategorized'}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-300">
                    {new Date(product.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 relative">
                    <button
                      onClick={() => setActionMenuOpen(actionMenuOpen === product.id ? null : product.id)}
                      className="text-gray-400 hover:text-gold transition-colors"
                    >
                      <MoreHorizontal className="w-4 h-4" />
                    </button>
                    
                    {actionMenuOpen === product.id && (
                      <div className="absolute right-0 mt-2 w-48 bg-black border border-gold/20 rounded-md shadow-lg z-10">
                        <div className="py-1">
                          <Link
                            href={`/product/${product.slug}`}
                            className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gold/10 hover:text-gold"
                          >
                            <Eye className="w-4 h-4 mr-2" />
                            View Product
                          </Link>
                          <Link
                            href={`/admin/products/${product.id}/edit`}
                            className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gold/10 hover:text-gold"
                          >
                            <Edit className="w-4 h-4 mr-2" />
                            Edit Product
                          </Link>
                          <button
                            onClick={() => handleFeaturedToggle(product.id, product.featured)}
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gold/10 hover:text-gold"
                          >
                            <Star className="w-4 h-4 mr-2" />
                            {product.featured ? 'Remove Featured' : 'Make Featured'}
                          </button>
                          <button
                            onClick={() => handleStatusChange(
                              product.id, 
                              product.status === 'active' ? 'inactive' : 'active'
                            )}
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gold/10 hover:text-gold"
                          >
                            {product.status === 'active' ? 'Deactivate' : 'Activate'}
                          </button>
                          <button
                            onClick={() => handleDelete(product.id)}
                            className="flex items-center w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-red-900/10"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete Product
                          </button>
                        </div>
                      </div>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}