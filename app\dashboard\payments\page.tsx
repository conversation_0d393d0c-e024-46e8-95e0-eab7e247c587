'use client';

import { useEffect, useState } from 'react';
import { Plus, CreditCard, Trash2, Star, Shield } from 'lucide-react';
import PaymentMethodCard from '@/components/dashboard/PaymentMethodCard';
import PaymentMethodModal from '@/components/dashboard/PaymentMethodModal';

interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'bank_account';
  provider: string;
  last_four: string;
  brand: string;
  exp_month: number;
  exp_year: number;
  cardholder_name: string;
  is_default: boolean;
  created_at: string;
}

export default function CustomerPayments() {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    fetchPaymentMethods();
  }, []);

  const fetchPaymentMethods = async () => {
    try {
      const response = await fetch('/api/user/payment-methods');
      if (!response.ok) {
        throw new Error('Failed to fetch payment methods');
      }
      const result = await response.json();
      setPaymentMethods(result.data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (paymentMethodId: string) => {
    if (!confirm('Are you sure you want to remove this payment method?')) return;

    try {
      const response = await fetch(`/api/user/payment-methods/${paymentMethodId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchPaymentMethods();
      }
    } catch (error) {
      console.error('Failed to delete payment method:', error);
    }
  };

  const handleSetDefault = async (paymentMethodId: string) => {
    try {
      const response = await fetch(`/api/user/payment-methods/${paymentMethodId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_default: true }),
      });

      if (response.ok) {
        fetchPaymentMethods();
      }
    } catch (error) {
      console.error('Failed to set default payment method:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gold"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-500 rounded-lg p-4">
        <p className="text-red-400">Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gold">Payment Methods</h1>
          <p className="text-gray-400 mt-1">Manage your payment options</p>
        </div>
        <button 
          onClick={() => setShowModal(true)}
          className="btn-primary flex items-center"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Payment Method
        </button>
      </div>

      {/* Security Notice */}
      <div className="bg-blue-900/20 border border-blue-500/20 rounded-lg p-4">
        <div className="flex items-center">
          <Shield className="w-5 h-5 text-blue-400 mr-3" />
          <div>
            <h3 className="text-sm font-medium text-blue-400">Secure Payment Processing</h3>
            <p className="text-xs text-gray-400 mt-1">
              Your payment information is encrypted and securely stored. We never store your full card details.
            </p>
          </div>
        </div>
      </div>

      {/* Payment Methods Grid */}
      {paymentMethods.length === 0 ? (
        <div className="bg-charcoal rounded-lg border border-gold/20 p-12 text-center">
          <CreditCard className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No payment methods saved</h3>
          <p className="text-gray-400 mb-6">
            Add a payment method to make checkout faster and more convenient
          </p>
          <button 
            onClick={() => setShowModal(true)}
            className="btn-primary"
          >
            Add Your First Payment Method
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {paymentMethods.map((paymentMethod) => (
            <PaymentMethodCard
              key={paymentMethod.id}
              paymentMethod={paymentMethod}
              onDelete={() => handleDelete(paymentMethod.id)}
              onSetDefault={() => handleSetDefault(paymentMethod.id)}
            />
          ))}
        </div>
      )}

      {/* Payment Method Modal */}
      {showModal && (
        <PaymentMethodModal
          onClose={() => setShowModal(false)}
          onSuccess={() => {
            setShowModal(false);
            fetchPaymentMethods();
          }}
        />
      )}
    </div>
  );
}