# Dashboard Setup Guide

The dashboard is currently showing "Error: Failed to fetch dashboard data" because the Supabase database hasn't been properly initialized with the required schema and functions.

## Issue Analysis

1. **Database Schema Missing**: The dashboard API routes are trying to query tables that don't exist yet
2. **Database Functions Missing**: Functions like `get_sales_analytics` and `get_low_stock_products` haven't been created
3. **Authentication Issues**: The NEXTAUTH_SECRET was set to a placeholder value (now fixed)

## Quick Fix (Temporary)

I've temporarily modified the dashboard API routes to use mock data and bypass authentication for testing:

- `/app/api/admin/dashboard/route.ts` - Now returns mock data
- `/app/api/customer/dashboard/route.ts` - Now returns mock data

This should make the dashboard load without errors, but with empty/mock data.

## Complete Fix (Recommended)

### Step 1: Set up Supabase Database

1. Go to your Supabase project dashboard: https://supabase.com/dashboard
2. Navigate to the SQL Editor
3. Run the schema from `lib/supabase/schema.sql`
4. Run the functions from `lib/supabase/functions.sql`
5. Run the sample data from `scripts/seed-data.sql`

### Step 2: Create Admin User

1. Sign up through the application at `/auth/signin`
2. Go to Supabase Auth section and find your user ID
3. Update the user's role to 'admin' in the users table:

```sql
UPDATE public.users 
SET role = 'admin' 
WHERE email = '<EMAIL>';
```

### Step 3: Restore Authentication

Once the database is set up, restore the authentication middleware:

In `app/api/admin/dashboard/route.ts`:
```typescript
export const GET = withAuth(getDashboardData, { requireAdmin: true });
```

In `app/api/customer/dashboard/route.ts`:
```typescript
export const GET = withAuth(getCustomerDashboard);
```

### Step 4: Test the Dashboard

1. Sign in as an admin user
2. Navigate to `/admin/dashboard`
3. The dashboard should now load with real data

## Environment Variables

Make sure these are set in `.env.local`:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
NEXTAUTH_SECRET=jooka-ecommerce-secret-key-2024-development
```

## Testing Endpoints

I've created test endpoints to help debug:

- `/api/test/dashboard` - Test basic database connection
- `/api/test/db-connection` - Test table access
- `/api/test/db-functions` - Test Supabase functions
- `/api/test/session` - Test authentication

## Current Status

✅ Dashboard components are working
✅ API routes are responding
✅ Authentication middleware is configured
❌ Database schema needs to be set up
❌ Sample data needs to be inserted
❌ Admin user needs to be created

Once you complete the database setup, the dashboard will work with real data instead of mock data.