import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { createServiceClient } from '@/lib/supabase/service';

export async function GET(request: NextRequest) {
  try {
    // Get the current JWT token
    const token = await getToken({ 
      req: request, 
      secret: process.env.NEXTAUTH_SECRET 
    });
    
    console.log('Current JWT token:', token);
    
    // Check user in database with service client
    const supabase = createServiceClient();
    
    let dbUser = null;
    let dbError = null;
    
    if (token?.email) {
      const { data, error } = await supabase
        .from('users')
        .select('id, email, role, email_verified')
        .eq('email', token.email)
        .single();
      
      dbUser = data;
      dbError = error;
    }
    
    return NextResponse.json({
      success: true,
      data: {
        hasToken: !!token,
        tokenData: token ? {
          email: token.email,
          role: token.role,
          sub: token.sub,
          emailVerified: token.emailVerified
        } : null,
        databaseUser: dbUser,
        databaseError: dbError?.message || null,
        mismatch: token && dbUser ? {
          rolesDifferent: token.role !== dbUser.role,
          tokenRole: token.role,
          dbRole: dbUser.role
        } : null
      }
    });
    
  } catch (error) {
    console.error('Debug auth error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to debug auth', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
