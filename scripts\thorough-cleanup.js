#!/usr/bin/env node

/**
 * Thorough Cleanup Script - Removes all traces of admin users
 */

const { createClient } = require('@supabase/supabase-js');

// Try to load dotenv
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function thoroughCleanup() {
  try {
    console.log('🧹 Thorough Cleanup for JOOKA E-commerce\n');
    
    // Step 1: Show current state
    console.log('🔍 Step 1: Current state...');
    
    const { data: allUsers, error: usersError } = await supabase
      .from('users')
      .select('id, email, role');
    
    if (usersError) {
      console.error('❌ Could not fetch users:', usersError.message);
      return;
    }
    
    console.log(`📊 Found ${allUsers.length} database users:`);
    allUsers.forEach(user => {
      console.log(`   - ${user.email} (${user.role}) - ID: ${user.id}`);
    });
    
    const { data: allProfiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, user_id, first_name, last_name');
    
    if (profilesError) {
      console.error('❌ Could not fetch profiles:', profilesError.message);
    } else {
      console.log(`📊 Found ${allProfiles.length} profiles:`);
      allProfiles.forEach(profile => {
        console.log(`   - ${profile.first_name} ${profile.last_name} - User ID: ${profile.user_id}`);
      });
    }
    
    // Check auth users
    const { data: authData, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('❌ Could not fetch auth users:', authError.message);
    } else {
      console.log(`📊 Found ${authData.users.length} auth users:`);
      authData.users.forEach(user => {
        console.log(`   - ${user.email} - ID: ${user.id}`);
      });
    }
    
    // Step 2: Clean up everything
    console.log('\n🧹 Step 2: Cleaning up all records...');
    
    // Delete all profiles first
    console.log('🔄 Deleting all profiles...');
    const { error: deleteProfilesError } = await supabase
      .from('profiles')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all
    
    if (deleteProfilesError) {
      console.warn('⚠️  Profile deletion warning:', deleteProfilesError.message);
    } else {
      console.log('✅ All profiles deleted');
    }
    
    // Delete all users
    console.log('🔄 Deleting all database users...');
    const { error: deleteUsersError } = await supabase
      .from('users')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all
    
    if (deleteUsersError) {
      console.warn('⚠️  User deletion warning:', deleteUsersError.message);
    } else {
      console.log('✅ All database users deleted');
    }
    
    // Delete all auth users
    if (authData && authData.users) {
      console.log('🔄 Deleting all auth users...');
      for (const user of authData.users) {
        console.log(`   Deleting auth user: ${user.email}`);
        const { error: deleteAuthError } = await supabase.auth.admin.deleteUser(user.id);
        if (deleteAuthError) {
          console.warn(`   ⚠️  Warning deleting ${user.email}:`, deleteAuthError.message);
        }
      }
      console.log('✅ All auth users deleted');
    }
    
    // Wait for cleanup
    console.log('⏳ Waiting for cleanup to complete...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Step 3: Verify cleanup
    console.log('\n🔍 Step 3: Verifying cleanup...');
    
    const { data: remainingUsers } = await supabase
      .from('users')
      .select('id, email');
    
    console.log(`📊 Remaining database users: ${remainingUsers ? remainingUsers.length : 0}`);
    
    const { data: remainingProfiles } = await supabase
      .from('profiles')
      .select('id');
    
    console.log(`📊 Remaining profiles: ${remainingProfiles ? remainingProfiles.length : 0}`);
    
    const { data: remainingAuth } = await supabase.auth.admin.listUsers();
    console.log(`📊 Remaining auth users: ${remainingAuth ? remainingAuth.users.length : 0}`);
    
    console.log('\n✅ Thorough cleanup completed!');
    console.log('\n📋 Next steps:');
    console.log('1. Run: node scripts/working-admin-create.js');
    console.log('2. This should now work without conflicts');
    console.log('3. Test your admin login');
    
  } catch (error) {
    console.error('\n❌ Cleanup failed:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  thoroughCleanup();
}

module.exports = { thoroughCleanup };