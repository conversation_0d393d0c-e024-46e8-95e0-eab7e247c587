// Orders API routes
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';
import { withDatabase } from '@/lib/database';
import { createClient } from '@/lib/supabase/server';
import { z } from 'zod';

const createOrderSchema = z.object({
  shippingAddress: z.object({
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    company: z.string().optional(),
    streetAddress1: z.string().min(1, 'Street address is required'),
    streetAddress2: z.string().optional(),
    city: z.string().min(1, 'City is required'),
    state: z.string().min(1, 'State is required'),
    postalCode: z.string().min(1, 'Postal code is required'),
    country: z.string().default('United States'),
    phone: z.string().optional(),
  }),
  billingAddress: z.object({
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    company: z.string().optional(),
    streetAddress1: z.string().min(1, 'Street address is required'),
    streetAddress2: z.string().optional(),
    city: z.string().min(1, 'City is required'),
    state: z.string().min(1, 'State is required'),
    postalCode: z.string().min(1, 'Postal code is required'),
    country: z.string().default('United States'),
    phone: z.string().optional(),
  }),
  paymentMethod: z.object({
    type: z.string().min(1, 'Payment method type is required'),
    provider: z.string().min(1, 'Payment provider is required'),
    paymentMethodId: z.string().min(1, 'Payment method ID is required'),
  }),
  cartItems: z.array(z.object({
    product_id: z.string(),
    quantity: z.number().min(1),
  })).min(1, 'Cart must contain at least one item'),
});

async function getOrders(request: NextRequest) {
  const user = (request as any).user;
  
  const result = await withDatabase(async (supabase) => {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || undefined;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    let query = supabase
      .from('orders')
      .select(`
        *,
        items:order_items(*),
        user:users(*)
      `, { count: 'exact' });

    // For customers, only show their own orders
    if (user.role !== 'admin') {
      query = query.eq('user_id', user.id);
    }

    if (status) {
      query = query.eq('status', status);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to).order('created_at', { ascending: false });

    const { data, error, count } = await query;
    if (error) throw error;

    return {
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    };
  });

  if (!result.success) {
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    );
  }

  return NextResponse.json(result.data);
}

async function createOrder(request: NextRequest) {
  const user = (request as any).user;
  
  try {
    const body = await request.json();
    const validationResult = createOrderSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { shippingAddress, billingAddress, paymentMethod, cartItems } = validationResult.data;

    const result = await withDatabase(async (supabase) => {
      // Validate cart items exist and are available
      for (const item of cartItems) {
        const { data: product, error } = await supabase
          .from('products')
          .select('*')
          .eq('id', item.product_id)
          .single();

        if (error || !product) {
          throw new Error(`Product ${item.product_id} not found`);
        }

        if (product.status !== 'active') {
          throw new Error(`Product ${product.name} is not available`);
        }

        if (product.track_inventory && product.inventory_count < item.quantity) {
          throw new Error(`Insufficient inventory for ${product.name}. Available: ${product.inventory_count}, Requested: ${item.quantity}`);
        }
      }

      // Calculate total amount
      let totalAmount = 0;
      const orderItems = [];

      for (const item of cartItems) {
        const { data: product } = await supabase
          .from('products')
          .select('price')
          .eq('id', item.product_id)
          .single();

        const itemTotal = product.price * item.quantity;
        totalAmount += itemTotal;

        orderItems.push({
          product_id: item.product_id,
          quantity: item.quantity,
          price: product.price,
          total: itemTotal
        });
      }

      // Create order
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          user_id: user.id,
          user_email: user.email,
          total_amount: totalAmount,
          status: 'pending',
          shipping_address: shippingAddress,
          billing_address: billingAddress,
          payment_method: paymentMethod
        })
        .select()
        .single();

      if (orderError) throw orderError;

      // Create order items
      const orderItemsWithOrderId = orderItems.map(item => ({
        ...item,
        order_id: order.id
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItemsWithOrderId);

      if (itemsError) throw itemsError;

      // Update inventory
      for (const item of cartItems) {
        const { data: currentProduct } = await supabase
          .from('products')
          .select('inventory_count')
          .eq('id', item.product_id)
          .single();

        const newInventoryCount = currentProduct.inventory_count - item.quantity;

        const { error: inventoryError } = await supabase
          .from('products')
          .update({ inventory_count: newInventoryCount })
          .eq('id', item.product_id);

        if (inventoryError) throw inventoryError;
      }

      // Clear user's cart
      await supabase
        .from('cart_items')
        .delete()
        .eq('user_id', user.id);

      return order.id;
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error?.message || 'Failed to create order' },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      message: 'Order created successfully',
      data: { orderId: result.data }
    }, { status: 201 });
  } catch (error) {
    console.error('Create order error:', error);
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    );
  }
}

export const GET = withAuth(getOrders);
export const POST = withAuth(createOrder);