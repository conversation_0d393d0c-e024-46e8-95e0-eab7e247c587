import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database/index';

export async function GET(request: NextRequest) {
  try {
    // Test basic database connection
    const categoriesResult = await db.getCategories();
    
    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      data: {
        categoriesCount: categoriesResult.data?.length || 0,
        hasError: !!categoriesResult.error
      }
    });
  } catch (error) {
    console.error('Test API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Database connection failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}