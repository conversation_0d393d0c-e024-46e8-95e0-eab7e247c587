#!/usr/bin/env node

/**
 * Clean and Create Admin <PERSON>ript - Clean up existing records and create fresh admin
 */

const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

// Try to load dotenv
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function cleanAndCreateAdmin() {
  try {
    console.log('🧹 JOOKA E-commerce Clean & Create Admin\n');
    
    // Get admin details
    const email = await question('Enter new admin email: ');
    const firstName = await question('Enter first name: ');
    const lastName = await question('Enter last name: ');
    const password = await question('Enter password (min 8 chars): ');
    
    rl.close();
    
    // Validate input
    if (!email || !firstName || !lastName || !password) {
      throw new Error('All fields are required');
    }
    
    if (password.length < 8) {
      throw new Error('Password must be at least 8 characters long');
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }
    
    console.log('\n🧹 Cleaning up existing records...');
    
    // First, clean up any existing records for this email
    console.log('🔄 Removing existing database records...');
    
    // Delete profile first (foreign key constraint)
    const { error: profileDeleteError } = await supabase
      .from('profiles')
      .delete()
      .in('user_id', 
        supabase
          .from('users')
          .select('id')
          .eq('email', email)
      );
    
    if (profileDeleteError) {
      console.warn('⚠️  Warning deleting profiles:', profileDeleteError.message);
    }
    
    // Delete user record
    const { error: userDeleteError } = await supabase
      .from('users')
      .delete()
      .eq('email', email);
    
    if (userDeleteError) {
      console.warn('⚠️  Warning deleting user:', userDeleteError.message);
    }
    
    // Clean up auth user if exists
    console.log('🔄 Checking for existing auth user...');
    const { data: existingAuthUser } = await supabase.auth.admin.getUserByEmail(email);
    
    if (existingAuthUser.user) {
      console.log('🔄 Removing existing auth user...');
      const { error: authDeleteError } = await supabase.auth.admin.deleteUser(existingAuthUser.user.id);
      
      if (authDeleteError) {
        console.warn('⚠️  Warning deleting auth user:', authDeleteError.message);
      } else {
        console.log('✅ Existing auth user removed');
      }
    }
    
    console.log('✅ Cleanup completed');
    
    // Now create fresh admin user
    console.log('\n🔄 Creating fresh admin user...');
    
    // Create auth user first
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        first_name: firstName,
        last_name: lastName,
      },
    });
    
    if (authError) {
      throw new Error(`Failed to create auth user: ${authError.message}`);
    }
    
    if (!authData.user) {
      throw new Error('Failed to create user account');
    }
    
    console.log('✅ Auth user created successfully');
    
    // Create database user record
    const { error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: email,
        email_verified: true,
        role: 'admin',
      });
    
    if (userError) {
      console.error('Detailed user creation error:', userError);
      throw new Error(`Failed to create user record: ${userError.message}`);
    }
    
    console.log('✅ Database user record created');
    
    // Create profile record
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        user_id: authData.user.id,
        first_name: firstName,
        last_name: lastName,
      });
    
    if (profileError) {
      console.warn('⚠️  Warning creating profile:', profileError.message);
    } else {
      console.log('✅ Profile created successfully');
    }
    
    console.log('\n🎉 Fresh admin user created successfully!');
    console.log(`📧 Email: ${email}`);
    console.log(`👤 Name: ${firstName} ${lastName}`);
    console.log(`🔑 Role: admin`);
    console.log(`🔐 Password: ${password}`);
    console.log('\n📋 Next steps:');
    console.log('1. Sign in at /auth/signin');
    console.log('2. You will be redirected to /admin/dashboard');
    console.log('3. Consider changing your password after first login');
    
    // Verify the creation
    console.log('\n🔍 Verifying creation...');
    const { data: verifyUser } = await supabase
      .from('users')
      .select('id, email, role')
      .eq('email', email)
      .single();
    
    if (verifyUser) {
      console.log('✅ Verification successful - admin user exists in database');
    } else {
      console.warn('⚠️  Warning: Could not verify user creation');
    }
    
  } catch (error) {
    console.error('\n❌ Failed to create admin user:', error.message);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Admin creation cancelled');
  rl.close();
  process.exit(0);
});

// Run the script
if (require.main === module) {
  cleanAndCreateAdmin();
}

module.exports = { cleanAndCreateAdmin };