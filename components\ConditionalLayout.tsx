'use client'

import { usePathname } from 'next/navigation'
import Navbar from '@/components/Navbar'
import Footer from '@/components/Footer'
import SessionProvider from '@/components/providers/SessionProvider'
import SupabaseProvider from '@/components/providers/SupabaseProvider'

export default function ConditionalLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const isDashboard = pathname?.startsWith('/dashboard') || pathname?.startsWith('/admin')

  return (
    <SessionProvider>
      <SupabaseProvider>
        {!isDashboard && <Navbar />}
        <main className="min-h-screen">{children}</main>
        {!isDashboard && <Footer />}
      </SupabaseProvider>
    </SessionProvider>
  )
}
