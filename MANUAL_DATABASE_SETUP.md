# Manual Database Setup Guide

Since the automated setup script is having issues, here's how to set up the database manually using the Supabase dashboard.

## Step 1: Create the Database Schema

1. **Go to your Supabase Dashboard**
2. **Navigate to SQL Editor**
3. **Copy and paste the entire contents of `lib/supabase/schema.sql`**
4. **Click "Run"**

## Step 2: Create Database Functions

1. **In the SQL Editor, copy and paste the entire contents of `lib/supabase/functions.sql`**
2. **Click "Run"**

## Step 3: Set up RLS Policies

1. **In the SQL Editor, copy and paste the entire contents of `lib/supabase/rls-policies.sql`**
2. **Click "Run"**

## Step 4: Create Your Admin User

1. **In the SQL Editor, run this command** (replace with your actual user ID):

```sql
-- Insert the admin user (bypasses RLS)
INSERT INTO public.users (id, email, email_verified, role)
VALUES ('c89ddf7c-30ae-4f54-828d-3e1c6a135238', '<EMAIL>', true, 'admin')
ON CONFLICT (id) DO UPDATE SET role = 'admin';

-- Insert the profile
INSERT INTO public.profiles (user_id, first_name, last_name)
VALUES ('c89ddf7c-30ae-4f54-828d-3e1c6a135238', 'Bikalpa', 'Shrestha')
ON CONFLICT (user_id) DO UPDATE SET 
  first_name = 'Bikalpa',
  last_name = 'Shrestha';
```

## Step 5: Add Sample Data (Optional)

```sql
-- Insert sample categories
INSERT INTO public.categories (name, slug, description, sort_order) VALUES
('Electronics', 'electronics', 'Electronic devices and accessories', 1),
('Clothing', 'clothing', 'Fashion and apparel', 2),
('Home & Garden', 'home-garden', 'Home improvement and garden supplies', 3),
('Books', 'books', 'Books and educational materials', 4),
('Sports & Outdoors', 'sports-outdoors', 'Sports equipment and outdoor gear', 5)
ON CONFLICT (slug) DO NOTHING;
```

## Step 6: Verify Setup

1. **Go to Table Editor in Supabase**
2. **Check that these tables exist:**
   - users
   - profiles
   - categories
   - products
   - orders
   - order_items
   - cart_items
   - addresses
   - payment_methods
   - notifications
   - inventory_transactions
   - audit_logs

3. **Check that your admin user exists in the `users` table**

## Step 7: Test Login

1. **Go to your app at `/auth/signin`**
2. **Sign in with:**
   - Email: `<EMAIL>`
   - Password: `incorrect9#`
3. **You should be redirected to `/admin/dashboard`**

## Troubleshooting

If you encounter any errors:

1. **Check the error message in the SQL Editor**
2. **Make sure you're running the SQL files in the correct order**
3. **Verify your user ID is correct** (check Authentication > Users in Supabase)
4. **Make sure RLS policies are applied after creating tables**

## Files to Execute (in order):

1. `lib/supabase/schema.sql` - Creates all tables
2. `lib/supabase/functions.sql` - Creates utility functions  
3. `lib/supabase/rls-policies.sql` - Sets up security policies
4. Admin user creation SQL (from Step 4 above)
5. Sample data SQL (optional, from Step 5 above)

This manual approach should work reliably since you're executing the SQL directly in the Supabase dashboard with full admin privileges.