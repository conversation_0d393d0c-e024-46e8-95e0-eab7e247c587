// Individual user management API routes
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';
import { createClient } from '@/lib/supabase/client';
import { db } from '@/lib/database';
import { z } from 'zod';

const updateUserRoleSchema = z.object({
  role: z.enum(['admin', 'customer']),
});

async function updateUserRole(request: NextRequest, { params }: { params: { id: string } }) {
  const userId = params.id;
  
  try {
    const body = await request.json();
    const validationResult = updateUserRoleSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { role } = validationResult.data;
    const supabase = createClient();

    const { data, error } = await supabase
      .from('users')
      .update({ role })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      message: 'User role updated successfully',
      data 
    });
  } catch (error) {
    console.error('Update user role error:', error);
    return NextResponse.json(
      { error: 'Failed to update user role' },
      { status: 500 }
    );
  }
}

async function deleteUser(request: NextRequest, { params }: { params: { id: string } }) {
  const userId = params.id;
  const currentUser = (request as any).user;

  try {
    // Prevent self-deletion
    if (userId === currentUser.id) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    const result = await db.deleteUser(userId);

    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Delete user error:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}

export const PUT = withAuth(updateUserRole, { requireAdmin: true });
export const DELETE = withAuth(deleteUser, { requireAdmin: true });