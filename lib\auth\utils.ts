// Authentication utility functions
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function createUserInDatabase(userData: {
  id: string;
  email: string;
  name?: string | null;
  image?: string | null;
  emailVerified?: boolean;
}) {
  try {
    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', userData.email)
      .single();

    if (existingUser) {
      return { success: true, userId: existingUser.id };
    }

    // Create user record
    const { error: userError } = await supabase
      .from('users')
      .insert({
        id: userData.id,
        email: userData.email,
        email_verified: userData.emailVerified || false,
        role: 'customer',
      });

    if (userError) {
      console.error('Error creating user:', userError);
      return { success: false, error: userError.message };
    }

    // Create profile record
    const names = userData.name?.split(' ') || [];
    const firstName = names[0] || '';
    const lastName = names.slice(1).join(' ') || '';

    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        user_id: userData.id,
        first_name: firstName,
        last_name: lastName,
        avatar_url: userData.image,
      });

    if (profileError) {
      console.error('Error creating profile:', profileError);
      // Don't fail the user creation if profile creation fails
    }

    return { success: true, userId: userData.id };
  } catch (error) {
    console.error('Database user creation error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

export async function getUserFromDatabase(email: string) {
  try {
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        profile:profiles(*)
      `)
      .eq('email', email)
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, user: data };
  } catch (error) {
    console.error('Get user error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}