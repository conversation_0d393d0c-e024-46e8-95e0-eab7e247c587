// Authentication middleware for route protection
import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string;
    email: string;
    role: string;
    emailVerified: boolean;
  };
}

// Middleware to check if user is authenticated
export async function requireAuth(request: NextRequest): Promise<NextResponse | null> {
  const token = await getToken({ 
    req: request, 
    secret: process.env.NEXTAUTH_SECRET 
  });

  if (!token) {
    return NextResponse.json(
      { error: 'Authentication required' },
      { status: 401 }
    );
  }

  // Add user info to request
  (request as AuthenticatedRequest).user = {
    id: token.sub!,
    email: token.email!,
    role: token.role as string,
    emailVerified: token.emailVerified as boolean,
  };

  return null; // Continue to the actual handler
}

// Middleware to check if user is admin
export async function requireAdmin(request: NextRequest): Promise<NextResponse | null> {
  const authResult = await requireAuth(request);
  if (authResult) return authResult;

  const user = (request as AuthenticatedRequest).user!;
  
  if (user.role !== 'admin') {
    return NextResponse.json(
      { error: 'Admin access required' },
      { status: 403 }
    );
  }

  return null; // Continue to the actual handler
}

// Middleware to check if user's email is verified
export async function requireEmailVerification(request: NextRequest): Promise<NextResponse | null> {
  const authResult = await requireAuth(request);
  if (authResult) return authResult;

  const user = (request as AuthenticatedRequest).user!;
  
  if (!user.emailVerified) {
    return NextResponse.json(
      { error: 'Email verification required' },
      { status: 403 }
    );
  }

  return null; // Continue to the actual handler
}

// Higher-order function to wrap API routes with authentication
export function withAuth(
  handler: (request: AuthenticatedRequest) => Promise<NextResponse>,
  options: {
    requireAdmin?: boolean;
    requireEmailVerification?: boolean;
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      // Check authentication
      const authResult = await requireAuth(request);
      if (authResult) return authResult;

      // Check admin requirement
      if (options.requireAdmin) {
        const adminResult = await requireAdmin(request);
        if (adminResult) return adminResult;
      }

      // Check email verification requirement
      if (options.requireEmailVerification) {
        const verificationResult = await requireEmailVerification(request);
        if (verificationResult) return verificationResult;
      }

      // Call the actual handler
      return await handler(request as AuthenticatedRequest);
    } catch (error) {
      console.error('Auth middleware error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

// Rate limiting middleware
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function withRateLimit(
  handler: (request: NextRequest) => Promise<NextResponse>,
  options: {
    maxRequests: number;
    windowMs: number;
  } = { maxRequests: 10, windowMs: 60000 } // 10 requests per minute by default
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const now = Date.now();
    const windowStart = now - options.windowMs;

    // Clean up old entries
    Array.from(rateLimitMap.entries()).forEach(([key, value]) => {
      if (value.resetTime < windowStart) {
        rateLimitMap.delete(key);
      }
    });

    // Check current rate limit
    const current = rateLimitMap.get(ip);
    if (current && current.count >= options.maxRequests && current.resetTime > windowStart) {
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      );
    }

    // Update rate limit
    if (current && current.resetTime > windowStart) {
      current.count++;
    } else {
      rateLimitMap.set(ip, { count: 1, resetTime: now });
    }

    return await handler(request);
  };
}

// CORS middleware
export function withCors(
  handler: (request: NextRequest) => Promise<NextResponse>,
  options: {
    origin?: string | string[];
    methods?: string[];
    headers?: string[];
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const response = await handler(request);

    // Set CORS headers
    const origin = options.origin || process.env.NEXTAUTH_URL || '*';
    const methods = options.methods || ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
    const headers = options.headers || ['Content-Type', 'Authorization'];

    response.headers.set('Access-Control-Allow-Origin', Array.isArray(origin) ? origin.join(',') : origin);
    response.headers.set('Access-Control-Allow-Methods', methods.join(','));
    response.headers.set('Access-Control-Allow-Headers', headers.join(','));
    response.headers.set('Access-Control-Max-Age', '86400');

    return response;
  };
}

// Input validation middleware
export function withValidation<T>(
  handler: (request: NextRequest, validatedData: T) => Promise<NextResponse>,
  schema: {
    parse: (data: any) => T;
  }
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      const body = await request.json();
      const validatedData = schema.parse(body);
      return await handler(request, validatedData);
    } catch (error) {
      console.error('Validation error:', error);
      return NextResponse.json(
        { error: 'Invalid request data', details: error },
        { status: 400 }
      );
    }
  };
}