// NextAuth configuration for JOOKA E-commerce Platform
import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import Facebook<PERSON>rovider from 'next-auth/providers/facebook';
import CredentialsProvider from 'next-auth/providers/credentials';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';
import { validateEmail, validatePassword } from '@/lib/validation';
import { createUserInDatabase, getUserFromDatabase } from './utils';
import { randomUUID } from 'crypto';

// Create Supabase client for NextAuth
const supabase = createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
        auth: {
            autoRefreshToken: false,
            persistSession: false,
        },
    }
);

export const authOptions: NextAuthOptions = {
    providers: [
        // Email/Password Provider
        CredentialsProvider({
            id: 'credentials',
            name: 'Email and Password',
            credentials: {
                email: { label: 'Email', type: 'email' },
                password: { label: 'Password', type: 'password' },
            },
            async authorize(credentials) {
                if (!credentials?.email || !credentials?.password) {
                    throw new Error('Email and password are required');
                }

                // Validate input
                if (!validateEmail(credentials.email)) {
                    throw new Error('Invalid email format');
                }

                if (!validatePassword(credentials.password)) {
                    throw new Error('Password must be at least 8 characters long');
                }

                try {
                    // Sign in with Supabase
                    const { data, error } = await supabase.auth.signInWithPassword({
                        email: credentials.email,
                        password: credentials.password,
                    });

                    if (error) {
                        throw new Error(error.message);
                    }

                    if (!data.user) {
                        throw new Error('Authentication failed');
                    }

                    // Get user profile
                    const { data: profile } = await supabase
                        .from('profiles')
                        .select('*')
                        .eq('user_id', data.user.id)
                        .single();

                    // Get user role
                    const { data: userData } = await supabase
                        .from('users')
                        .select('role')
                        .eq('id', data.user.id)
                        .single();

                    return {
                        id: data.user.id,
                        email: data.user.email!,
                        name: profile ? `${profile.first_name} ${profile.last_name}`.trim() : null,
                        image: profile?.avatar_url || null,
                        role: userData?.role || 'customer',
                        emailVerified: data.user.email_confirmed_at ? new Date(data.user.email_confirmed_at) : null,
                    };
                } catch (error) {
                    console.error('Authentication error:', error);
                    throw new Error('Invalid credentials');
                }
            },
        }),

        // Google OAuth Provider (only if configured)
        ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? [
            GoogleProvider({
                clientId: process.env.GOOGLE_CLIENT_ID,
                clientSecret: process.env.GOOGLE_CLIENT_SECRET,
                authorization: {
                    params: {
                        prompt: 'consent',
                        access_type: 'offline',
                        response_type: 'code',
                    },
                },
            })
        ] : []),

        // Facebook OAuth Provider (only if configured)
        ...(process.env.FACEBOOK_CLIENT_ID && process.env.FACEBOOK_CLIENT_SECRET ? [
            FacebookProvider({
                clientId: process.env.FACEBOOK_CLIENT_ID,
                clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
            })
        ] : []),
    ],

    session: {
        strategy: 'jwt',
        maxAge: 30 * 24 * 60 * 60, // 30 days
    },

    jwt: {
        maxAge: 30 * 24 * 60 * 60, // 30 days
    },

    pages: {
        signIn: '/auth/signin',
        error: '/auth/error',
        verifyRequest: '/auth/verify-request',
    },

    callbacks: {
        async signIn({ user, account, profile }) {
            try {
                // For OAuth providers, ensure user exists in our database
                if (account?.provider !== 'credentials' && user.email) {
                    const userId = user.id || randomUUID();

                    const result = await createUserInDatabase({
                        id: userId,
                        email: user.email,
                        name: user.name,
                        image: user.image,
                        emailVerified: true, // OAuth users are considered verified
                    });

                    if (!result.success) {
                        console.error('Failed to create user in database:', result.error);
                        return false;
                    }

                    // Update user ID if it was generated
                    if (!user.id) {
                        user.id = userId;
                    }
                }

                return true;
            } catch (error) {
                console.error('Sign in error:', error);
                return false;
            }
        },

        async jwt({ token, user, account }) {
            // Initial sign in
            if (user) {
                token.role = (user as any).role || 'customer';
                token.emailVerified = (user as any).emailVerified || false;
            }

            // Refresh user data on each request (but not too frequently)
            if (token.email && (!token.lastRefresh || Date.now() - (token.lastRefresh as number) > 5 * 60 * 1000)) {
                try {
                    const result = await getUserFromDatabase(token.email);

                    if (result.success && result.user) {
                        token.role = result.user.role;
                        token.emailVerified = result.user.email_verified;
                        token.lastRefresh = Date.now();
                    }
                } catch (error) {
                    console.error('Error refreshing user data:', error);
                }
            }

            return token;
        },

        async session({ session, token }) {
            if (session.user) {
                session.user.id = token.sub!;
                session.user.role = token.role as string;
                session.user.emailVerified = token.emailVerified as boolean;
            }

            return session;
        },

        async redirect({ url, baseUrl }) {
            // Allows relative callback URLs
            if (url.startsWith('/')) return `${baseUrl}${url}`;
            // Allows callback URLs on the same origin
            else if (new URL(url).origin === baseUrl) return url;
            return baseUrl;
        },
    },

    events: {
        async signIn({ user, account, profile, isNewUser }) {
            console.log('User signed in:', { userId: user.id, email: user.email, provider: account?.provider });

            // Track sign in event
            if (isNewUser) {
                console.log('New user registered:', user.email);
            }
        },

        async signOut({ session, token }) {
            console.log('User signed out:', { userId: token?.sub });
        },
    },

    debug: process.env.NODE_ENV === 'development',
};