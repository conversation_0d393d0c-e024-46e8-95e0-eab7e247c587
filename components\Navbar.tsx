'use client'

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { useSession, signOut } from 'next-auth/react';
import { cn } from '@/lib/utils';

interface NavLink {
  label: string;
  href: string;
}

interface NavbarProps {
  logoSrc?: string;
  logoAlt?: string;
  navLinks?: NavLink[];
  className?: string;
}

const defaultNavLinks: NavLink[] = [
  { label: 'HOME', href: '/' },
  { label: 'SHOP', href: '/shop' },
  { label: 'CART', href: '/cart' },
  { label: 'LOGIN', href: '/auth/signin' },
  { label: 'SIGN UP', href: '/auth/signup' },
];

// Helper component for navigation links - matching MinimalistHero style
const NavLink = ({ href, children }: { href: string; children: React.ReactNode }) => (
  <Link
    href={href}
    className="text-sm font-medium tracking-widest text-foreground/60 transition-colors hover:text-foreground"
  >
    {children}
  </Link>
);

const Navbar = ({
  logoSrc = "/logo.png",
  logoAlt = "Jooka Logo",
  navLinks = defaultNavLinks,
  className = ""
}: NavbarProps) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { data: session, status } = useSession();

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  // Dynamic navigation links based on authentication status
  const getNavLinks = () => {
    const baseLinks = [
      { label: 'HOME', href: '/' },
      { label: 'SHOP', href: '/shop' },
      { label: 'CART', href: '/cart' },
    ];

    if (status === 'loading') {
      return baseLinks;
    }

    if (session?.user) {
      // Authenticated user links
      const userLinks = [
        ...baseLinks,
        { 
          label: session.user.role === 'admin' ? 'ADMIN' : 'DASHBOARD', 
          href: session.user.role === 'admin' ? '/admin/dashboard' : '/dashboard' 
        },
      ];
      return userLinks;
    } else {
      // Unauthenticated user links
      return [
        ...baseLinks,
        { label: 'LOGIN', href: '/auth/signin' },
        { label: 'SIGN UP', href: '/auth/signup' },
      ];
    }
  };

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/' });
    closeMobileMenu();
  };

  const dynamicNavLinks = getNavLinks();

  return (
    <>
      {/* Main Navbar - Matching MinimalistHero header styling exactly */}
      <header className={cn(
        'fixed top-0 left-0 right-0 z-50 flex w-full items-center justify-between overflow-hidden bg-background px-6 py-4 font-sans md:px-8 md:py-6',
        className
      )}>
        <div className="z-30 flex w-full max-w-7xl items-center justify-between mx-auto">
          {/* Logo - Using image instead of text */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="flex items-center"
          >
            <Link href="/" className="hover:opacity-80 transition-opacity">
              <Image
                src={logoSrc}
                alt={logoAlt}
                width={120}
                height={40}
                className="h-8 w-auto md:h-10"
                priority
              />
            </Link>
          </motion.div>

          {/* Desktop Navigation - Matching MinimalistHero styling */}
          <div className="hidden items-center space-x-8 md:flex">
            {dynamicNavLinks.map((link, index) => (
              <motion.div
                key={link.label}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <NavLink href={link.href}>
                  {link.label}
                </NavLink>
              </motion.div>
            ))}
            
            {/* Sign Out Button for Authenticated Users */}
            {session?.user && (
              <motion.button
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: dynamicNavLinks.length * 0.1 }}
                onClick={handleSignOut}
                className="text-sm font-medium tracking-widest text-foreground/60 transition-colors hover:text-foreground"
              >
                SIGN OUT
              </motion.button>
            )}
          </div>

          {/* Mobile Menu Button - Matching MinimalistHero styling exactly */}
          <motion.button
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="flex flex-col space-y-1.5 md:hidden"
            onClick={toggleMobileMenu}
            aria-label="Toggle mobile menu"
          >
            <motion.span 
              className="block h-0.5 w-6 bg-foreground transition-all duration-300"
              animate={isMobileMenuOpen ? { rotate: 45, y: 6 } : { rotate: 0, y: 0 }}
            />
            <motion.span 
              className="block h-0.5 w-6 bg-foreground transition-all duration-300"
              animate={isMobileMenuOpen ? { opacity: 0 } : { opacity: 1 }}
            />
            <motion.span 
              className="block h-0.5 w-5 bg-foreground transition-all duration-300"
              animate={isMobileMenuOpen ? { rotate: -45, y: -6, width: 24 } : { rotate: 0, y: 0, width: 20 }}
            />
          </motion.button>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm md:hidden"
            onClick={closeMobileMenu}
          />
        )}
      </AnimatePresence>

      {/* Mobile Menu Panel */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'tween', duration: 0.3 }}
            className="fixed top-0 right-0 bottom-0 z-50 w-80 max-w-[85vw] bg-background border-l border-foreground/20 md:hidden"
          >
            {/* Mobile Menu Header */}
            <div className="flex items-center justify-between px-6 py-4 border-b border-foreground/20">
              <Image
                src={logoSrc}
                alt={logoAlt}
                width={100}
                height={32}
                className="h-6 w-auto"
              />
              <button
                onClick={closeMobileMenu}
                className="p-2 text-foreground hover:text-foreground/80 transition-colors"
                aria-label="Close mobile menu"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Mobile Menu Links */}
            <div className="flex flex-col px-6 py-4 space-y-6">
              {dynamicNavLinks.map((link, index) => (
                <motion.div
                  key={link.label}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Link
                    href={link.href}
                    onClick={closeMobileMenu}
                    className="block text-lg font-medium tracking-widest text-foreground/60 hover:text-foreground transition-colors duration-300 py-2"
                  >
                    {link.label}
                  </Link>
                </motion.div>
              ))}
              
              {/* Mobile Sign Out Button */}
              {session?.user && (
                <motion.button
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: dynamicNavLinks.length * 0.1 }}
                  onClick={handleSignOut}
                  className="block text-lg font-medium tracking-widest text-foreground/60 hover:text-foreground transition-colors duration-300 py-2 text-left"
                >
                  SIGN OUT
                </motion.button>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Spacer to prevent content from hiding behind fixed navbar */}
      <div className="h-10 md:h-12"></div>
    </>
  );
};

export default Navbar;