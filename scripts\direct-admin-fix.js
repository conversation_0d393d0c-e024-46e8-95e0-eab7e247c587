#!/usr/bin/env node

/**
 * Direct Admin Fix Script for JOOKA E-commerce Platform
 * This script directly creates the database record for an existing auth user
 */

const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

// Try to load dotenv, but don't fail if it's not available
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function directAdminFix() {
  try {
    console.log('🔧 JOOKA E-commerce Direct Admin Fix\n');
    console.log('This script will create database records for your admin user.');
    console.log('Make sure you have the correct user ID from Supabase Auth.\n');
    
    // Get the details
    const email = await question('Enter admin email: ');
    const userId = await question('Enter user ID (from Supabase Auth dashboard): ');
    const firstName = await question('Enter first name: ');
    const lastName = await question('Enter last name: ');
    
    rl.close();
    
    if (!email || !userId || !firstName) {
      throw new Error('Email, user ID, and first name are required');
    }
    
    console.log('\n🔄 Creating database records...');
    
    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();
    
    if (existingUser) {
      console.log('👤 User already exists in database');
      
      if (existingUser.role === 'admin') {
        console.log('✅ User is already an admin');
      } else {
        console.log('🔄 Promoting to admin...');
        
        const { error: updateError } = await supabase
          .from('users')
          .update({ role: 'admin' })
          .eq('id', existingUser.id);
        
        if (updateError) {
          throw new Error(`Failed to promote user: ${updateError.message}`);
        }
        
        console.log('✅ User promoted to admin');
      }
    } else {
      console.log('🔄 Creating user record...');
      
      const { error: userError } = await supabase
        .from('users')
        .insert({
          id: userId,
          email: email,
          email_verified: true,
          role: 'admin',
        });
      
      if (userError) {
        console.error('Detailed error:', userError);
        throw new Error(`Failed to create user: ${userError.message}`);
      }
      
      console.log('✅ User record created');
    }
    
    // Check if profile exists
    const { data: existingProfile } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (existingProfile) {
      console.log('✅ Profile already exists');
    } else {
      console.log('🔄 Creating profile...');
      
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          user_id: userId,
          first_name: firstName,
          last_name: lastName || '',
        });
      
      if (profileError) {
        console.error('Profile error:', profileError);
        console.warn('⚠️  Warning: Could not create profile, but user record was created');
      } else {
        console.log('✅ Profile created');
      }
    }
    
    console.log('\n🎉 Admin fix completed successfully!');
    console.log(`📧 Email: ${email}`);
    console.log(`🆔 User ID: ${userId}`);
    console.log(`👤 Name: ${firstName} ${lastName || ''}`);
    console.log(`🔑 Role: admin`);
    console.log('\n📋 Next steps:');
    console.log('1. Try signing in at /auth/signin');
    console.log('2. You should be redirected to /admin/dashboard');
    console.log('3. If login fails, check the user ID is correct');
    
  } catch (error) {
    console.error('\n❌ Failed to fix admin user:', error.message);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Fix cancelled');
  rl.close();
  process.exit(0);
});

// Run the script
if (require.main === module) {
  directAdminFix();
}

module.exports = { directAdminFix };