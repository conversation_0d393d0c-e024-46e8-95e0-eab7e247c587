# JOOKA E-commerce Authentication Guide

This guide explains how authentication works in the JOOKA E-commerce platform and how to manage user accounts.

## Overview

The platform uses a dual authentication system:
- **NextAuth.js** for session management and OAuth providers
- **Supabase Auth** for user storage and authentication
- **Role-based access control** with `customer` and `admin` roles

## User Types

### Customer Accounts
- Created through the public signup page at `/auth/signup`
- Default role: `customer`
- Access to: shopping, cart, orders, profile management
- Dashboard: `/dashboard`

### Admin Accounts
- Created through secure methods (see Admin Creation below)
- Role: `admin`
- Access to: all customer features + admin panel
- Dashboard: `/admin/dashboard`
- Can manage: users, products, orders, inventory

## Navigation

The navbar dynamically shows different links based on authentication status:

### Unauthenticated Users
- HOME
- SHOP
- CART
- LOGIN
- SIGN UP

### Authenticated Customers
- HOME
- SHOP
- CART
- DASHBOARD
- SIGN OUT

### Authenticated Admins
- HOME
- SHOP
- CART
- ADMIN (links to admin dashboard)
- SIGN OUT

## Admin Account Creation

There are several ways to create admin accounts:

### 1. Command Line Script (Recommended)

Use the secure command line script:

```bash
npm run admin:create
```

This script will:
- Prompt for admin details (email, name, password)
- Create the user in Supabase Auth
- Set the role to `admin`
- Create the user profile
- Auto-confirm the email

### 2. Promote Existing Users (Development Only)

In development mode, admins can promote existing users:

1. Go to `/admin/user-management`
2. Enter the user's email address
3. Click "Promote to Admin"

**Note**: This method is only available in development mode.

### 3. Database Direct Access

Manually update the user role in Supabase:

1. Go to your Supabase dashboard
2. Navigate to the `users` table
3. Find the user by email
4. Change the `role` field from `'customer'` to `'admin'`

### 4. API Endpoint (Development Only)

POST request to `/api/admin/promote-user`:

```json
{
  "email": "<EMAIL>"
}
```

**Note**: Only works in development and requires admin authentication.

## Authentication Flow

### Customer Registration
1. User visits `/auth/signup`
2. Fills out registration form
3. API creates user in Supabase Auth
4. Database trigger creates user record with `customer` role
5. Profile record is created
6. Email verification sent
7. User can sign in after verification

### Admin Registration
1. Admin runs `npm run admin:create` script
2. Script prompts for admin details
3. User created in Supabase Auth with auto-confirmation
4. User record created with `admin` role
5. Profile record created
6. Admin can immediately sign in

### Sign In Process
1. User visits `/auth/signin`
2. Enters credentials
3. NextAuth validates with Supabase
4. Session created with user role
5. Redirect based on role:
   - Admin → `/admin/dashboard`
   - Customer → `/dashboard` or callback URL

## Role-Based Access Control

### Route Protection

Routes are protected using middleware and API route guards:

```typescript
// Middleware protects routes
export { default } from "next-auth/middleware"

export const config = {
  matcher: ["/dashboard/:path*", "/admin/:path*"]
}
```

### API Route Protection

```typescript
import { withAuth } from '@/lib/auth/middleware';

// Customer-only endpoint
export const GET = withAuth(handler);

// Admin-only endpoint  
export const POST = withAuth(handler, { requireAdmin: true });
```

### Database Security

Row Level Security (RLS) policies ensure:
- Users can only access their own data
- Admins have full access to all data
- Public read access for products and categories

## Session Management

- Sessions last 30 days
- JWT tokens refresh user data every 5 minutes
- Role changes require re-authentication
- Sign out clears session and redirects to home

## Security Features

- Password validation (8+ chars, mixed case, numbers, symbols)
- Email verification required
- CSRF protection via NextAuth
- Secure session storage
- RLS policies in database
- Admin-only API endpoints

## Troubleshooting

### Common Issues

1. **User can't access admin features**
   - Check user role in database
   - Verify session contains correct role
   - Try signing out and back in

2. **Admin creation fails**
   - Check Supabase service role key
   - Verify database connection
   - Check for existing user with same email

3. **Authentication errors**
   - Verify environment variables
   - Check Supabase project settings
   - Review NextAuth configuration

### Environment Variables

Required variables in `.env.local`:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key

# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_secret

# OAuth (optional)
GOOGLE_CLIENT_ID=your_google_id
GOOGLE_CLIENT_SECRET=your_google_secret
FACEBOOK_CLIENT_ID=your_facebook_id
FACEBOOK_CLIENT_SECRET=your_facebook_secret
```

## Best Practices

1. **Admin Creation**: Always use the command line script in production
2. **Role Changes**: Require re-authentication after role changes
3. **Session Security**: Use secure, httpOnly cookies
4. **Database Access**: Never bypass RLS policies
5. **API Security**: Always validate user permissions
6. **Password Policy**: Enforce strong passwords
7. **Email Verification**: Require verification for all accounts

## Development vs Production

### Development Mode
- User promotion UI available
- API promotion endpoint enabled
- Relaxed security for testing
- Detailed error messages

### Production Mode
- User promotion UI disabled
- API promotion endpoint disabled
- Strict security enforcement
- Generic error messages
- Admin creation via CLI only

## Next Steps

After setting up authentication:

1. Test customer registration and login
2. Create your first admin account
3. Test admin dashboard access
4. Configure OAuth providers (optional)
5. Set up email templates in Supabase
6. Configure production environment variables