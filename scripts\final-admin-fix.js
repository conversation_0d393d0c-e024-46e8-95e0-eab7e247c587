#!/usr/bin/env node

/**
 * Final Admin Fix Script - Handles all edge cases
 */

const { createClient } = require('@supabase/supabase-js');

// Try to load dotenv
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// ===== EDIT THESE ADMIN DETAILS =====
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_FIRST_NAME = 'Admin';
const ADMIN_LAST_NAME = 'User';
const ADMIN_PASSWORD = 'AdminPassword123!';
// ====================================

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function finalAdminFix() {
  try {
    console.log('🔧 Final Admin Fix for JOOKA E-commerce\n');
    console.log(`📧 Target Email: ${ADMIN_EMAIL}`);
    console.log(`👤 Name: ${ADMIN_FIRST_NAME} ${ADMIN_LAST_NAME}`);
    console.log('');
    
    // Step 1: Check current state
    console.log('🔍 Step 1: Checking current state...');
    
    // Check database users
    const { data: dbUsers, error: dbError } = await supabase
      .from('users')
      .select('id, email, role')
      .eq('email', ADMIN_EMAIL);
    
    if (dbError) {
      console.warn('⚠️  Could not check database users:', dbError.message);
    } else {
      console.log(`📊 Found ${dbUsers.length} database users with this email`);
      dbUsers.forEach(user => {
        console.log(`   - ID: ${user.id}, Role: ${user.role}`);
      });
    }
    
    // Check auth users
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.warn('⚠️  Could not check auth users:', authError.message);
    } else {
      const matchingAuthUsers = authUsers.users.filter(u => u.email === ADMIN_EMAIL);
      console.log(`📊 Found ${matchingAuthUsers.length} auth users with this email`);
      matchingAuthUsers.forEach(user => {
        console.log(`   - ID: ${user.id}, Confirmed: ${!!user.email_confirmed_at}`);
      });
    }
    
    // Step 2: Clean up everything
    console.log('\n🧹 Step 2: Complete cleanup...');
    
    // Delete all database records for this email
    if (dbUsers && dbUsers.length > 0) {
      for (const user of dbUsers) {
        console.log(`🔄 Cleaning up database user: ${user.id}`);
        
        // Delete profile
        await supabase
          .from('profiles')
          .delete()
          .eq('user_id', user.id);
        
        // Delete user
        await supabase
          .from('users')
          .delete()
          .eq('id', user.id);
      }
      console.log('✅ Database records cleaned');
    }
    
    // Delete all auth users for this email
    if (authUsers && authUsers.users) {
      const matchingAuthUsers = authUsers.users.filter(u => u.email === ADMIN_EMAIL);
      for (const user of matchingAuthUsers) {
        console.log(`🔄 Cleaning up auth user: ${user.id}`);
        await supabase.auth.admin.deleteUser(user.id);
      }
      if (matchingAuthUsers.length > 0) {
        console.log('✅ Auth records cleaned');
      }
    }
    
    // Wait a moment for cleanup to complete
    console.log('⏳ Waiting for cleanup to complete...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Step 3: Create fresh admin user
    console.log('\n🔄 Step 3: Creating fresh admin user...');
    
    const { data: authData, error: createAuthError } = await supabase.auth.admin.createUser({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
      email_confirm: true,
      user_metadata: {
        first_name: ADMIN_FIRST_NAME,
        last_name: ADMIN_LAST_NAME,
      },
    });
    
    if (createAuthError) {
      throw new Error(`Auth creation failed: ${createAuthError.message}`);
    }
    
    if (!authData.user) {
      throw new Error('No user returned from auth creation');
    }
    
    console.log('✅ Auth user created successfully');
    console.log(`   User ID: ${authData.user.id}`);
    
    // Step 4: Create database user
    console.log('\n🔄 Step 4: Creating database user...');
    
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: ADMIN_EMAIL,
        email_verified: true,
        role: 'admin',
      })
      .select()
      .single();
    
    if (userError) {
      throw new Error(`Database user creation failed: ${userError.message}`);
    }
    
    console.log('✅ Database user created successfully');
    
    // Step 5: Create profile
    console.log('\n🔄 Step 5: Creating profile...');
    
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .insert({
        user_id: authData.user.id,
        first_name: ADMIN_FIRST_NAME,
        last_name: ADMIN_LAST_NAME,
      })
      .select()
      .single();
    
    if (profileError) {
      console.warn('⚠️  Profile creation warning:', profileError.message);
    } else {
      console.log('✅ Profile created successfully');
    }
    
    // Step 6: Final verification
    console.log('\n🔍 Step 6: Final verification...');
    
    const { data: finalCheck, error: checkError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        role,
        email_verified,
        profiles (
          first_name,
          last_name
        )
      `)
      .eq('email', ADMIN_EMAIL)
      .single();
    
    if (checkError) {
      console.warn('⚠️  Verification failed:', checkError.message);
    } else {
      console.log('✅ Final verification successful:');
      console.log(`   ID: ${finalCheck.id}`);
      console.log(`   Email: ${finalCheck.email}`);
      console.log(`   Role: ${finalCheck.role}`);
      console.log(`   Verified: ${finalCheck.email_verified}`);
      
      if (finalCheck.profiles && finalCheck.profiles.length > 0) {
        const profile = finalCheck.profiles[0];
        console.log(`   Name: ${profile.first_name} ${profile.last_name}`);
      }
    }
    
    console.log('\n🎉 Admin user fixed and ready!');
    console.log('\n📋 Login Credentials:');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log(`🔐 Password: ${ADMIN_PASSWORD}`);
    console.log('\n📋 Test Your Admin Account:');
    console.log('1. Start your dev server: npm run dev');
    console.log('2. Go to: http://localhost:3000/auth/signin');
    console.log('3. Sign in with the credentials above');
    console.log('4. You should be redirected to: /admin/dashboard');
    console.log('5. Verify you can access admin features');
    console.log('\n✨ Your admin account is now working!');
    
  } catch (error) {
    console.error('\n❌ Final admin fix failed:', error.message);
    console.error('\n🆘 If this still doesn\'t work:');
    console.error('1. Check your Supabase dashboard manually');
    console.error('2. Verify your service role key has admin permissions');
    console.error('3. Make sure your database schema is correct');
    console.error('4. Try creating the user manually in Supabase dashboard');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  finalAdminFix();
}

module.exports = { finalAdminFix };