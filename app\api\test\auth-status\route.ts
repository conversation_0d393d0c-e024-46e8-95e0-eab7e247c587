import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';
import { createServiceClient } from '@/lib/supabase/service';

async function checkAuthStatus(request: NextRequest) {
  try {
    const user = (request as any).user;
    console.log('Current authenticated user:', user);
    
    // Check if user exists in database with service client
    const supabase = createServiceClient();
    
    const { data: dbUser, error: dbError } = await supabase
      .from('users')
      .select('*')
      .eq('email', user.email)
      .single();
    
    console.log('User in database:', { dbUser, dbError });
    
    // Check all users in database
    const { data: allUsers, error: allUsersError } = await supabase
      .from('users')
      .select('id, email, role')
      .limit(10);
    
    console.log('All users in database:', { allUsers, allUsersError });
    
    return NextResponse.json({
      success: true,
      data: {
        authenticatedUser: {
          id: user.id,
          email: user.email,
          role: user.role,
          emailVerified: user.emailVerified
        },
        databaseUser: dbUser,
        databaseError: dbError?.message || null,
        allUsers: allUsers || [],
        allUsersError: allUsersError?.message || null
      }
    });
    
  } catch (error) {
    console.error('Auth status check error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check auth status', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export const GET = withAuth(checkAuthStatus, { requireAdmin: true });
