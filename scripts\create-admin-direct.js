#!/usr/bin/env node

/**
 * Direct Admin Creation Script - No interactive input
 * Edit the admin details directly in this file and run it
 */

const { createClient } = require('@supabase/supabase-js');

// Try to load dotenv
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// ===== EDIT THESE ADMIN DETAILS =====
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_FIRST_NAME = 'Admin';
const ADMIN_LAST_NAME = 'User';
const ADMIN_PASSWORD = 'AdminPassword123!';
// ====================================

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function createAdminUser() {
  try {
    console.log('🔐 JOOKA E-commerce Direct Admin Creation\n');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log(`👤 Name: ${ADMIN_FIRST_NAME} ${ADMIN_LAST_NAME}`);
    console.log('🔄 Creating admin user...\n');
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(ADMIN_EMAIL)) {
      throw new Error('Invalid email format');
    }
    
    if (ADMIN_PASSWORD.length < 8) {
      throw new Error('Password must be at least 8 characters long');
    }
    
    // Check if user already exists in auth
    const { data: existingAuthUser } = await supabase.auth.admin.getUserByEmail(ADMIN_EMAIL);
    
    if (existingAuthUser.user) {
      console.log('👤 Auth user already exists, checking database record...');
      
      // Check if user exists in our users table
      const { data: existingUser } = await supabase
        .from('users')
        .select('id, role')
        .eq('email', ADMIN_EMAIL)
        .single();
      
      if (existingUser) {
        if (existingUser.role === 'admin') {
          console.log('✅ User already exists and is an admin');
          console.log('\n🎉 Admin user is ready to use!');
          return;
        } else {
          // Promote existing user to admin
          console.log('👤 User exists as customer, promoting to admin...');
          
          const { error: updateError } = await supabase
            .from('users')
            .update({ role: 'admin' })
            .eq('id', existingUser.id);
          
          if (updateError) {
            throw new Error(`Failed to promote user to admin: ${updateError.message}`);
          }
          
          console.log('✅ User successfully promoted to admin');
          console.log('\n🎉 Admin user is ready to use!');
          return;
        }
      } else {
        // Auth user exists but no database record - create it
        console.log('🔄 Creating database record for existing auth user...');
        
        const { error: userError } = await supabase
          .from('users')
          .insert({
            id: existingAuthUser.user.id,
            email: ADMIN_EMAIL,
            email_verified: true,
            role: 'admin',
          });
        
        if (userError) {
          console.error('Detailed user creation error:', userError);
          throw new Error(`Failed to create user record: ${userError.message || 'Unknown error'}`);
        }
        
        console.log('✅ Database user record created successfully');
        
        // Create profile record
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            user_id: existingAuthUser.user.id,
            first_name: ADMIN_FIRST_NAME,
            last_name: ADMIN_LAST_NAME,
          });
        
        if (profileError) {
          console.warn('⚠️  Warning creating profile:', profileError.message);
        } else {
          console.log('✅ Profile created successfully');
        }
        
        console.log('\n🎉 Admin user setup completed successfully!');
        return;
      }
    }
    
    // Create new admin user with Supabase Auth
    console.log('🔄 Creating new auth user...');
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
      email_confirm: true, // Auto-confirm admin users
      user_metadata: {
        first_name: ADMIN_FIRST_NAME,
        last_name: ADMIN_LAST_NAME,
      },
    });
    
    if (authError) {
      throw new Error(`Failed to create auth user: ${authError.message}`);
    }
    
    if (!authData.user) {
      throw new Error('Failed to create user account');
    }
    
    console.log('✅ Auth user created successfully');
    
    // Create user record with admin role
    console.log('🔄 Creating database user record...');
    const { error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: ADMIN_EMAIL,
        email_verified: true,
        role: 'admin',
      });
    
    if (userError) {
      console.error('Detailed user creation error:', userError);
      throw new Error(`Failed to create user record: ${userError.message || 'Unknown error'}`);
    }
    
    console.log('✅ User record created successfully');
    
    // Create profile record
    console.log('🔄 Creating user profile...');
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        user_id: authData.user.id,
        first_name: ADMIN_FIRST_NAME,
        last_name: ADMIN_LAST_NAME,
      });
    
    if (profileError) {
      console.warn('⚠️  Warning creating profile:', profileError.message);
    } else {
      console.log('✅ Profile created successfully');
    }
    
    console.log('\n🎉 Admin user created successfully!');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log(`👤 Name: ${ADMIN_FIRST_NAME} ${ADMIN_LAST_NAME}`);
    console.log(`🔑 Role: admin`);
    console.log(`🔐 Password: ${ADMIN_PASSWORD}`);
    console.log('\n📋 Next steps:');
    console.log('1. The admin can now sign in at /auth/signin');
    console.log('2. They will be redirected to /admin/dashboard after login');
    console.log('3. Admin has full access to all admin features');
    console.log('\n⚠️  Remember to change the password after first login!');
    
  } catch (error) {
    console.error('\n❌ Failed to create admin user:', error.message);
    console.error('\n🔧 Troubleshooting tips:');
    console.error('1. Check your .env.local file has correct Supabase credentials');
    console.error('2. Verify your Supabase project is active');
    console.error('3. Make sure the database schema is properly set up');
    console.error('4. Check if the email already exists in Supabase Auth dashboard');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  createAdminUser();
}

module.exports = { createAdminUser };