// Update admin profile script
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateAdminProfile() {
  const adminId = 'debebb25-21a7-4810-be1f-a9bf3dde78f0';
  
  try {
    console.log('👤 Updating admin profile...');
    
    // Create/update profile
    const { error: profileError } = await supabase
      .from('profiles')
      .upsert({
        user_id: adminId,
        first_name: 'Admin',
        last_name: 'User'
      });
    
    if (profileError) {
      console.error('❌ Profile error:', profileError.message);
      return;
    }
    
    console.log('✅ Admin profile updated successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('👤 Name: Admin User');
    console.log('🔑 You can sign in at /auth/signin');
    
  } catch (error) {
    console.error('❌ Error updating admin profile:', error);
  }
}

updateAdminProfile();