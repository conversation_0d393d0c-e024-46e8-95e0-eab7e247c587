#!/usr/bin/env node

/**
 * Working Admin Creation Script - Uses correct Supabase API
 */

const { createClient } = require('@supabase/supabase-js');

// Try to load dotenv
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// ===== EDIT THESE ADMIN DETAILS =====
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_FIRST_NAME = 'Admin';
const ADMIN_LAST_NAME = 'User';
const ADMIN_PASSWORD = 'AdminPassword123!';
// ====================================

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function createWorkingAdmin() {
  try {
    console.log('🔐 Working Admin Creation for JOOKA E-commerce\n');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log(`👤 Name: ${ADMIN_FIRST_NAME} ${ADMIN_LAST_NAME}`);
    console.log(`🔐 Password: ${ADMIN_PASSWORD}`);
    console.log('');
    
    // Step 1: Clean up existing database records
    console.log('🧹 Step 1: Cleaning up existing database records...');
    
    // Get existing user ID if exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', ADMIN_EMAIL)
      .single();
    
    if (existingUser) {
      console.log('🔄 Found existing user, cleaning up database...');
      
      // Delete profile first (foreign key constraint)
      const { error: profileDeleteError } = await supabase
        .from('profiles')
        .delete()
        .eq('user_id', existingUser.id);
      
      if (profileDeleteError) {
        console.warn('⚠️  Profile delete warning:', profileDeleteError.message);
      }
      
      // Delete user
      const { error: userDeleteError } = await supabase
        .from('users')
        .delete()
        .eq('id', existingUser.id);
      
      if (userDeleteError) {
        console.warn('⚠️  User delete warning:', userDeleteError.message);
      } else {
        console.log('✅ Existing database records cleaned');
      }
    }
    
    // Step 2: Create auth user using the correct API
    console.log('\n🔄 Step 2: Creating auth user...');
    
    // Use the correct admin API method
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
      email_confirm: true,
      user_metadata: {
        first_name: ADMIN_FIRST_NAME,
        last_name: ADMIN_LAST_NAME,
      },
    });
    
    if (authError) {
      console.error('Auth error details:', authError);
      throw new Error(`Auth creation failed: ${authError.message}`);
    }
    
    if (!authData.user) {
      throw new Error('No user returned from auth creation');
    }
    
    console.log('✅ Auth user created successfully');
    console.log(`   User ID: ${authData.user.id}`);
    console.log(`   Email: ${authData.user.email}`);
    
    // Step 3: Create database user
    console.log('\n🔄 Step 3: Creating database user...');
    
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: ADMIN_EMAIL,
        email_verified: true,
        role: 'admin',
      })
      .select()
      .single();
    
    if (userError) {
      console.error('User creation error details:', userError);
      throw new Error(`Database user creation failed: ${userError.message}`);
    }
    
    console.log('✅ Database user created successfully');
    console.log(`   User ID: ${userData.id}`);
    console.log(`   Role: ${userData.role}`);
    
    // Step 4: Create profile
    console.log('\n🔄 Step 4: Creating user profile...');
    
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .insert({
        user_id: authData.user.id,
        first_name: ADMIN_FIRST_NAME,
        last_name: ADMIN_LAST_NAME,
      })
      .select()
      .single();
    
    if (profileError) {
      console.warn('⚠️  Profile creation warning:', profileError.message);
    } else {
      console.log('✅ Profile created successfully');
      console.log(`   Profile ID: ${profileData.id}`);
    }
    
    // Step 5: Final verification
    console.log('\n🔍 Step 5: Final verification...');
    
    const { data: verifyData, error: verifyError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        role,
        email_verified,
        created_at,
        profiles (
          id,
          first_name,
          last_name
        )
      `)
      .eq('email', ADMIN_EMAIL)
      .single();
    
    if (verifyError) {
      console.warn('⚠️  Verification warning:', verifyError.message);
    } else {
      console.log('✅ Final verification successful:');
      console.log(`   Database ID: ${verifyData.id}`);
      console.log(`   Email: ${verifyData.email}`);
      console.log(`   Role: ${verifyData.role}`);
      console.log(`   Email Verified: ${verifyData.email_verified}`);
      console.log(`   Created: ${verifyData.created_at}`);
      
      if (verifyData.profiles && verifyData.profiles.length > 0) {
        const profile = verifyData.profiles[0];
        console.log(`   Profile: ${profile.first_name} ${profile.last_name}`);
      }
    }
    
    // Step 6: Test auth user exists
    console.log('\n🔍 Step 6: Verifying auth user...');
    
    try {
      const { data: authUsers, error: listError } = await supabase.auth.admin.listUsers();
      
      if (listError) {
        console.warn('⚠️  Could not list auth users:', listError.message);
      } else {
        const adminAuthUser = authUsers.users.find(u => u.email === ADMIN_EMAIL);
        if (adminAuthUser) {
          console.log('✅ Auth user verified:');
          console.log(`   Auth ID: ${adminAuthUser.id}`);
          console.log(`   Email Confirmed: ${!!adminAuthUser.email_confirmed_at}`);
        } else {
          console.warn('⚠️  Auth user not found in list');
        }
      }
    } catch (listErr) {
      console.warn('⚠️  Could not verify auth user:', listErr.message);
    }
    
    console.log('\n🎉 Admin user created successfully!');
    console.log('\n📋 Login Details:');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log(`🔐 Password: ${ADMIN_PASSWORD}`);
    console.log('\n📋 Next Steps:');
    console.log('1. Start your development server: npm run dev');
    console.log('2. Go to http://localhost:3000/auth/signin');
    console.log('3. Sign in with the above credentials');
    console.log('4. You should be redirected to /admin/dashboard');
    console.log('5. Change your password in the admin settings');
    console.log('\n✨ Your admin account is ready to use!');
    
  } catch (error) {
    console.error('\n❌ Admin creation failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Check your Supabase credentials in .env.local');
    console.error('2. Verify your Supabase project is active');
    console.error('3. Make sure the database schema is set up');
    console.error('4. Check Supabase dashboard for any issues');
    console.error('5. Try running: node scripts/check-database.js');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  createWorkingAdmin();
}

module.exports = { createWorkingAdmin };