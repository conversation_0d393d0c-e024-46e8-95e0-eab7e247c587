#!/usr/bin/env node

/**
 * Test script to verify Supabase authentication configuration
 * Run with: node scripts/test-supabase-auth.js
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase configuration');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testAuth() {
  console.log('🔍 Testing Supabase Authentication Configuration\n');
  
  const testEmail = '<EMAIL>';
  const testPassword = 'TestPass123!';
  
  console.log(`📧 Testing with email: ${testEmail}`);
  console.log(`🔐 Testing with password: ${testPassword}\n`);
  
  try {
    console.log('🚀 Attempting signup...');
    const { data, error } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          first_name: 'Test',
          last_name: 'User',
        }
      }
    });
    
    if (error) {
      console.error('❌ Signup failed:', error.message);
      console.error('Error code:', error.code || 'unknown');
      
      if (error.message.includes('email_address_invalid')) {
        console.log('\n💡 Possible solutions:');
        console.log('1. Check Supabase Dashboard → Authentication → Settings');
        console.log('2. Disable email domain restrictions');
        console.log('3. Add allowed email domains');
        console.log('4. Check if email provider is enabled');
      }
    } else {
      console.log('✅ Signup successful!');
      console.log('User ID:', data.user?.id);
      console.log('Email confirmed:', data.user?.email_confirmed_at ? 'Yes' : 'No');
      
      if (!data.user?.email_confirmed_at) {
        console.log('\n📧 Email confirmation required');
        console.log('Check your email for verification link');
      }
    }
    
  } catch (err) {
    console.error('❌ Unexpected error:', err.message);
  }
}

testAuth();
