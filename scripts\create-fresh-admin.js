#!/usr/bin/env node

/**
 * Create Fresh Admin Script for JOOKA E-commerce Platform
 * This script creates a completely new admin user (auth + database)
 */

const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

// Try to load dotenv, but don't fail if it's not available
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

function questionHidden(prompt) {
  return new Promise((resolve) => {
    process.stdout.write(prompt);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    let password = '';
    process.stdin.on('data', function(char) {
      char = char + '';
      
      switch (char) {
        case '\n':
        case '\r':
        case '\u0004':
          process.stdin.setRawMode(false);
          process.stdin.pause();
          process.stdout.write('\n');
          resolve(password);
          break;
        case '\u0003':
          process.exit();
          break;
        case '\u007f': // Backspace
          if (password.length > 0) {
            password = password.slice(0, -1);
            process.stdout.write('\b \b');
          }
          break;
        default:
          password += char;
          process.stdout.write('*');
          break;
      }
    });
  });
}

async function createFreshAdmin() {
  try {
    console.log('🆕 JOOKA E-commerce Fresh Admin Creation\n');
    
    // Get admin details
    const email = await question('Enter admin email: ');
    const firstName = await question('Enter first name: ');
    const lastName = await question('Enter last name: ');
    const password = await questionHidden('Enter password (min 8 chars): ');
    const confirmPassword = await questionHidden('Confirm password: ');
    
    rl.close();
    
    // Validate input
    if (!email || !firstName || !lastName || !password) {
      throw new Error('All fields are required');
    }
    
    if (password !== confirmPassword) {
      throw new Error('Passwords do not match');
    }
    
    if (password.length < 8) {
      throw new Error('Password must be at least 8 characters long');
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }
    
    console.log('\n🔄 Creating fresh admin user...');
    
    // First, delete any existing auth user with this email
    try {
      const { data: existingUsers } = await supabase.auth.admin.listUsers();
      const existingUser = existingUsers.users.find(user => user.email === email);
      
      if (existingUser) {
        console.log('🗑️  Removing existing auth user...');
        await supabase.auth.admin.deleteUser(existingUser.id);
      }
    } catch (error) {
      console.log('⚠️  Could not check/remove existing user, continuing...');
    }
    
    // Create new auth user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm admin users
      user_metadata: {
        first_name: firstName,
        last_name: lastName,
      },
    });
    
    if (authError) {
      throw new Error(`Failed to create auth user: ${authError.message}`);
    }
    
    if (!authData.user) {
      throw new Error('Failed to create user account');
    }
    
    console.log('✅ Auth user created successfully');
    
    // Delete any existing database user record
    await supabase
      .from('users')
      .delete()
      .eq('email', email);
    
    await supabase
      .from('profiles')
      .delete()
      .eq('user_id', authData.user.id);
    
    // Create database user record
    const { error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: email,
        email_verified: true,
        role: 'admin',
      });
    
    if (userError) {
      console.error('Database user error:', userError);
      throw new Error(`Failed to create database user: ${userError.message}`);
    }
    
    console.log('✅ Database user record created');
    
    // Create profile record
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        user_id: authData.user.id,
        first_name: firstName,
        last_name: lastName,
      });
    
    if (profileError) {
      console.warn('⚠️  Warning creating profile:', profileError.message);
    } else {
      console.log('✅ Profile created successfully');
    }
    
    console.log('\n🎉 Fresh admin user created successfully!');
    console.log(`📧 Email: ${email}`);
    console.log(`🆔 User ID: ${authData.user.id}`);
    console.log(`👤 Name: ${firstName} ${lastName}`);
    console.log(`🔑 Role: admin`);
    console.log('\n📋 Next steps:');
    console.log('1. Go to /auth/signin');
    console.log('2. Sign in with the email and password you just created');
    console.log('3. You should be redirected to /admin/dashboard');
    
  } catch (error) {
    console.error('\n❌ Failed to create fresh admin:', error.message);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Creation cancelled');
  rl.close();
  process.exit(0);
});

// Run the script
if (require.main === module) {
  createFreshAdmin();
}

module.exports = { createFreshAdmin };