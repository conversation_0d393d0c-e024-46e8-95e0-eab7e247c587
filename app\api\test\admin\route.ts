// Test API route to verify admin authentication middleware
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';

async function handler(request: NextRequest) {
  const user = (request as any).user;
  
  return NextResponse.json({
    message: 'Admin authentication successful',
    user: {
      id: user.id,
      email: user.email,
      role: user.role,
      emailVerified: user.emailVerified,
    },
  });
}

export const GET = withAuth(handler, { requireAdmin: true });
export const POST = withAuth(handler, { requireAdmin: true });