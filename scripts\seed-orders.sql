-- <PERSON><PERSON> orders data for JOOKA E-commerce Platform
-- Run this after setting up the schema and basic seed data
-- This script creates realistic sample orders to populate the dashboard

-- First, let's get some existing data to work with
DO $$
DECLARE
    customer_id UUID;
    admin_id UUID;
    product_ids UUID[];
    category_ids UUID[];
    order_id UUID;
    order_number TEXT;
    i INTEGER;
BEGIN
    -- Get existing user IDs
    SELECT id INTO customer_id FROM public.users WHERE email = '<EMAIL>' LIMIT 1;
    SELECT id INTO admin_id FROM public.users WHERE email = '<EMAIL>' LIMIT 1;
    
    -- If users don't exist, create them
    IF customer_id IS NULL THEN
        INSERT INTO public.users (id, email, role, email_verified) VALUES
            ('00000000-0000-0000-0000-000000000002', '<EMAIL>', 'customer', true)
        ON CONFLICT (email) DO UPDATE SET role = 'customer'
        RETURNING id INTO customer_id;
        
        INSERT INTO public.profiles (user_id, first_name, last_name) VALUES
            (customer_id, '<PERSON>', 'Doe')
        ON CONFLICT (user_id) DO UPDATE SET first_name = '<PERSON>', last_name = 'Doe';
    END IF;
    
    IF admin_id IS NULL THEN
        INSERT INTO public.users (id, email, role, email_verified) VALUES
            ('00000000-0000-0000-0000-000000000001', '<EMAIL>', 'admin', true)
        ON CONFLICT (email) DO UPDATE SET role = 'admin'
        RETURNING id INTO admin_id;
        
        INSERT INTO public.profiles (user_id, first_name, last_name) VALUES
            (admin_id, 'Admin', 'User')
        ON CONFLICT (user_id) DO UPDATE SET first_name = 'Admin', last_name = 'User';
    END IF;
    
    -- Get existing product IDs
    SELECT ARRAY(SELECT id FROM public.products WHERE status = 'active' LIMIT 8) INTO product_ids;
    
    -- If no products exist, create some basic ones
    IF array_length(product_ids, 1) IS NULL OR array_length(product_ids, 1) = 0 THEN
        -- First ensure we have categories
        INSERT INTO public.categories (name, slug, description, is_active, sort_order) VALUES
            ('Electronics', 'electronics', 'Electronic devices and gadgets', true, 1),
            ('Clothing', 'clothing', 'Fashion and apparel', true, 2)
        ON CONFLICT (slug) DO NOTHING;
        
        -- Get category IDs
        SELECT ARRAY(SELECT id FROM public.categories LIMIT 2) INTO category_ids;
        
        -- Create some products
        INSERT INTO public.products (name, slug, description, short_description, price, category_id, inventory_count, status, featured) VALUES
            ('Wireless Headphones', 'wireless-headphones', 'High-quality wireless headphones', 'Premium headphones', 199.99, category_ids[1], 50, 'active', true),
            ('Smartphone', 'smartphone', 'Latest smartphone with advanced features', 'Advanced smartphone', 699.99, category_ids[1], 25, 'active', true),
            ('T-Shirt', 't-shirt', 'Comfortable cotton t-shirt', 'Cotton t-shirt', 29.99, category_ids[2], 100, 'active', false),
            ('Jeans', 'jeans', 'Classic denim jeans', 'Denim jeans', 79.99, category_ids[2], 75, 'active', true);
        
        -- Get the newly created product IDs
        SELECT ARRAY(SELECT id FROM public.products WHERE status = 'active' LIMIT 4) INTO product_ids;
    END IF;
    
    -- Create sample addresses for the customer
    INSERT INTO public.addresses (user_id, type, first_name, last_name, company, address_line_1, address_line_2, city, state, postal_code, country, phone, is_default) VALUES
        (customer_id, 'shipping', 'John', 'Doe', NULL, '123 Main St', 'Apt 4B', 'New York', 'NY', '10001', 'US', '******-0123', true),
        (customer_id, 'billing', 'John', 'Doe', NULL, '123 Main St', 'Apt 4B', 'New York', 'NY', '10001', 'US', '******-0123', true)
    ON CONFLICT DO NOTHING;
    
    -- Create sample orders with different statuses and dates
    FOR i IN 1..15 LOOP
        order_id := uuid_generate_v4();
        order_number := 'JOO' || TO_CHAR(NOW() - (i || ' days')::INTERVAL, 'YYYYMMDD') || LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
        
        -- Insert order with varying dates (last 30 days)
        INSERT INTO public.orders (
            id, 
            order_number, 
            user_id, 
            user_email,
            status, 
            subtotal, 
            tax_amount, 
            shipping_amount, 
            total_amount,
            shipping_address,
            billing_address,
            payment_method,
            created_at,
            updated_at
        ) VALUES (
            order_id,
            order_number,
            customer_id,
            '<EMAIL>',
            CASE 
                WHEN i <= 2 THEN 'pending'
                WHEN i <= 5 THEN 'processing'
                WHEN i <= 8 THEN 'shipped'
                WHEN i <= 12 THEN 'delivered'
                ELSE 'cancelled'
            END,
            ROUND((RANDOM() * 500 + 50)::NUMERIC, 2), -- Random subtotal between $50-$550
            ROUND((RANDOM() * 50 + 5)::NUMERIC, 2),   -- Random tax between $5-$55
            ROUND((RANDOM() * 20 + 5)::NUMERIC, 2),   -- Random shipping between $5-$25
            0, -- Will be calculated
            jsonb_build_object(
                'first_name', 'John',
                'last_name', 'Doe',
                'address_line_1', '123 Main St',
                'city', 'New York',
                'state', 'NY',
                'postal_code', '10001',
                'country', 'US'
            ),
            jsonb_build_object(
                'first_name', 'John',
                'last_name', 'Doe',
                'address_line_1', '123 Main St',
                'city', 'New York',
                'state', 'NY',
                'postal_code', '10001',
                'country', 'US'
            ),
            jsonb_build_object(
                'type', 'credit_card',
                'last_four', '4242',
                'brand', 'visa'
            ),
            NOW() - (i || ' days')::INTERVAL,
            NOW() - (i || ' days')::INTERVAL
        );
        
        -- Update total_amount
        UPDATE public.orders 
        SET total_amount = subtotal + tax_amount + shipping_amount 
        WHERE id = order_id;
        
        -- Add 1-3 random items to each order
        FOR j IN 1..(1 + FLOOR(RANDOM() * 3)::INTEGER) LOOP
            INSERT INTO public.order_items (
                order_id,
                product_id,
                quantity,
                unit_price,
                total_price
            ) VALUES (
                order_id,
                product_ids[1 + FLOOR(RANDOM() * array_length(product_ids, 1))::INTEGER],
                1 + FLOOR(RANDOM() * 3)::INTEGER, -- 1-3 quantity
                ROUND((RANDOM() * 200 + 20)::NUMERIC, 2), -- Random price $20-$220
                0 -- Will be calculated
            );
            
            -- Update total_price for the item
            UPDATE public.order_items 
            SET total_price = quantity * unit_price 
            WHERE order_id = order_id AND product_id = product_ids[1 + FLOOR(RANDOM() * array_length(product_ids, 1))::INTEGER];
        END LOOP;
    END LOOP;
    
    RAISE NOTICE 'Sample orders created successfully!';
END $$;

-- Create some additional recent orders for today to show live activity
INSERT INTO public.orders (
    order_number, 
    user_id, 
    user_email,
    status, 
    subtotal, 
    tax_amount, 
    shipping_amount, 
    total_amount,
    shipping_address,
    billing_address,
    payment_method,
    created_at,
    updated_at
) 
SELECT 
    'JOO' || TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0'),
    (SELECT id FROM public.users WHERE email = '<EMAIL>' LIMIT 1),
    '<EMAIL>',
    'pending',
    ROUND((RANDOM() * 300 + 50)::NUMERIC, 2),
    ROUND((RANDOM() * 30 + 5)::NUMERIC, 2),
    ROUND((RANDOM() * 15 + 5)::NUMERIC, 2),
    0,
    jsonb_build_object(
        'first_name', 'John',
        'last_name', 'Doe',
        'address_line_1', '123 Main St',
        'city', 'New York',
        'state', 'NY',
        'postal_code', '10001',
        'country', 'US'
    ),
    jsonb_build_object(
        'first_name', 'John',
        'last_name', 'Doe',
        'address_line_1', '123 Main St',
        'city', 'New York',
        'state', 'NY',
        'postal_code', '10001',
        'country', 'US'
    ),
    jsonb_build_object(
        'type', 'credit_card',
        'last_four', '4242',
        'brand', 'visa'
    ),
    NOW() - (FLOOR(RANDOM() * 24) || ' hours')::INTERVAL,
    NOW() - (FLOOR(RANDOM() * 24) || ' hours')::INTERVAL
FROM generate_series(1, 5);

-- Update all total_amounts
UPDATE public.orders 
SET total_amount = subtotal + tax_amount + shipping_amount 
WHERE total_amount = 0;

-- Add some low stock products for testing
UPDATE public.products 
SET inventory_count = FLOOR(RANDOM() * 5)::INTEGER 
WHERE name IN ('Wireless Headphones', 'Smartphone') 
AND inventory_count > 10;

-- Create some notifications for testing
INSERT INTO public.notifications (user_id, type, title, message, data) 
SELECT 
    (SELECT id FROM public.users WHERE role = 'admin' LIMIT 1),
    'order',
    'New Order Received',
    'Order #' || order_number || ' has been placed',
    jsonb_build_object('order_id', id, 'order_number', order_number)
FROM public.orders 
WHERE created_at > NOW() - INTERVAL '1 day'
LIMIT 3;

-- Final verification
SELECT 
    'Orders created: ' || COUNT(*) as summary
FROM public.orders;

SELECT 
    'Order items created: ' || COUNT(*) as summary  
FROM public.order_items;
