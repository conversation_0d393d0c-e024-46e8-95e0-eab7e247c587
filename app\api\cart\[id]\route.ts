// Individual cart item API routes
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';
import { db } from '@/lib/database';
import { z } from 'zod';

const updateCartItemSchema = z.object({
  quantity: z.number().min(1, 'Quantity must be at least 1'),
});

async function updateCartItem(request: NextRequest, { params }: { params: { id: string } }) {
  const cartItemId = params.id;
  
  try {
    const body = await request.json();
    const validationResult = updateCartItemSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { quantity } = validationResult.data;

    const result = await db.updateCartItem(cartItemId, quantity);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      message: 'Cart item updated successfully',
      data: result.data 
    });
  } catch (error) {
    console.error('Update cart item error:', error);
    return NextResponse.json(
      { error: 'Failed to update cart item' },
      { status: 500 }
    );
  }
}

async function removeCartItem(request: NextRequest, { params }: { params: { id: string } }) {
  const cartItemId = params.id;
  
  try {
    const result = await db.removeFromCart(cartItemId);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ message: 'Item removed from cart successfully' });
  } catch (error) {
    console.error('Remove cart item error:', error);
    return NextResponse.json(
      { error: 'Failed to remove item from cart' },
      { status: 500 }
    );
  }
}

export const PUT = withAuth(updateCartItem);
export const DELETE = withAuth(removeCartItem);