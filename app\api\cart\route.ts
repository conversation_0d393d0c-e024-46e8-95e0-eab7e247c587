// Shopping cart API routes
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';
import { db } from '@/lib/database';
import { z } from 'zod';

const addToCartSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
});

const updateCartItemSchema = z.object({
  quantity: z.number().min(1, 'Quantity must be at least 1'),
});

async function getCart(request: NextRequest) {
  const user = (request as any).user;
  
  try {
    const result = await db.getCartItems(user.id);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    // Calculate cart totals
    const items = result.data || [];
    const subtotal = items.reduce((total, item) => {
      return total + (item.product.price * item.quantity);
    }, 0);

    const cartData = {
      items,
      itemCount: items.reduce((total, item) => total + item.quantity, 0),
      subtotal,
    };

    return NextResponse.json({ data: cartData });
  } catch (error) {
    console.error('Get cart error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch cart' },
      { status: 500 }
    );
  }
}

async function addToCart(request: NextRequest) {
  const user = (request as any).user;
  
  try {
    const body = await request.json();
    const validationResult = addToCartSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { productId, quantity } = validationResult.data;

    // Check if product exists and is available
    const productResult = await db.getProduct(productId);
    if (productResult.error || !productResult.data) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    const product = productResult.data;
    
    // Check if product is active
    if (product.status !== 'active') {
      return NextResponse.json(
        { error: 'Product is not available' },
        { status: 400 }
      );
    }

    // Check inventory if tracking is enabled
    if (product.track_inventory && product.inventory_count < quantity) {
      return NextResponse.json(
        { error: `Only ${product.inventory_count} items available in stock` },
        { status: 400 }
      );
    }

    const result = await db.addToCart(user.id, productId, quantity);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      message: 'Item added to cart successfully',
      data: result.data 
    });
  } catch (error) {
    console.error('Add to cart error:', error);
    return NextResponse.json(
      { error: 'Failed to add item to cart' },
      { status: 500 }
    );
  }
}

async function clearCart(request: NextRequest) {
  const user = (request as any).user;
  
  try {
    const result = await db.clearCart(user.id);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({ message: 'Cart cleared successfully' });
  } catch (error) {
    console.error('Clear cart error:', error);
    return NextResponse.json(
      { error: 'Failed to clear cart' },
      { status: 500 }
    );
  }
}

export const GET = withAuth(getCart);
export const POST = withAuth(addToCart);
export const DELETE = withAuth(clearCart);