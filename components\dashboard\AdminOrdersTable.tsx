import { useState, useEffect, useRef } from 'react';
import { MoreHorizontal, Eye, Edit, Package, Truck, CheckCircle, X, Trash2 } from 'lucide-react';
import Link from 'next/link';

interface Order {
  id: string;
  user_email: string;
  total_amount: number;
  status: string;
  created_at: string;
  items_count: number;
  shipping_address: any;
  payment_status: string;
}

interface AdminOrdersTableProps {
  orders: Order[];
  onOrderUpdate: () => void;
}

const statusColors = {
  pending: 'bg-yellow-900/20 text-yellow-400 border-yellow-500/20',
  processing: 'bg-blue-900/20 text-blue-400 border-blue-500/20',
  shipped: 'bg-purple-900/20 text-purple-400 border-purple-500/20',
  delivered: 'bg-green-900/20 text-green-400 border-green-500/20',
  cancelled: 'bg-red-900/20 text-red-400 border-red-500/20',
};

const statusIcons = {
  pending: Package,
  processing: Package,
  shipped: Truck,
  delivered: CheckCircle,
  cancelled: X,
};

export default function AdminOrdersTable({ orders, onOrderUpdate }: AdminOrdersTableProps) {
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setActionMenuOpen(null);
      }
    }

    if (actionMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [actionMenuOpen]);

  const handleStatusChange = async (orderId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        onOrderUpdate();
        setActionMenuOpen(null);
        alert('Order status updated successfully!');
      } else {
        const result = await response.json();
        alert(`Failed to update order status: ${result.error}`);
      }
    } catch (error) {
      console.error('Failed to update order status:', error);
      alert('Failed to update order status. Please try again.');
    }
  };

  const getNextStatus = (currentStatus: string) => {
    const statusFlow = {
      pending: 'processing',
      processing: 'shipped',
      shipped: 'delivered',
      delivered: 'delivered', // No next status
      cancelled: 'cancelled', // No next status
    };
    return statusFlow[currentStatus as keyof typeof statusFlow];
  };

  const getStatusLabel = (status: string) => {
    const labels = {
      pending: 'Mark as Processing',
      processing: 'Mark as Shipped',
      shipped: 'Mark as Delivered',
      delivered: 'Already Delivered',
      cancelled: 'Order Cancelled',
    };
    return labels[status as keyof typeof labels];
  };

  const handleDelete = async (orderId: string, orderNumber: string) => {
    if (!confirm(`Are you sure you want to delete order "${orderNumber}"? This action cannot be undone.`)) return;

    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        onOrderUpdate();
        setActionMenuOpen(null);
        alert('Order deleted successfully!');
      } else {
        const result = await response.json();
        alert(`Failed to delete order: ${result.error}`);
      }
    } catch (error) {
      console.error('Failed to delete order:', error);
      alert('Failed to delete order. Please try again.');
    }
  };

  return (
    <div className="bg-charcoal rounded-lg border border-gold/20 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gold/20 bg-gold/5">
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Order ID
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Customer
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Items
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Total
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Payment
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gold/20">
            {orders.length === 0 ? (
              <tr>
                <td colSpan={8} className="px-6 py-8 text-center text-gray-400">
                  <Package className="w-12 h-12 mx-auto mb-4 text-gray-600" />
                  <p>No orders found</p>
                </td>
              </tr>
            ) : (
              orders.map((order) => {
                const StatusIcon = statusIcons[order.status as keyof typeof statusIcons] || Package;
                const nextStatus = getNextStatus(order.status);
                
                return (
                  <tr key={order.id} className="hover:bg-gold/5">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-white">
                        #{order.id.slice(-8)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-300">{order.user_email}</div>
                      {order.shipping_address && (
                        <div className="text-xs text-gray-500">
                          {order.shipping_address.city}, {order.shipping_address.state}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {order.items_count || 0} items
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                      ${order.total_amount.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${
                        statusColors[order.status as keyof typeof statusColors] || 
                        'bg-gray-900/20 text-gray-400 border-gray-500/20'
                      }`}>
                        <StatusIcon className="w-3 h-3 mr-1" />
                        {order.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        order.payment_status === 'paid' 
                          ? 'bg-green-900/20 text-green-400'
                          : order.payment_status === 'pending'
                          ? 'bg-yellow-900/20 text-yellow-400'
                          : 'bg-red-900/20 text-red-400'
                      }`}>
                        {order.payment_status || 'pending'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {new Date(order.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap relative">
                      <button
                        onClick={() => setActionMenuOpen(actionMenuOpen === order.id ? null : order.id)}
                        className="text-gray-400 hover:text-gold transition-colors"
                      >
                        <MoreHorizontal className="w-4 h-4" />
                      </button>
                      
                      {actionMenuOpen === order.id && (
                        <div ref={menuRef} className="absolute right-0 mt-2 w-56 bg-black border border-gold/20 rounded-md shadow-lg z-50">
                          <div className="py-1">
                            <Link
                              href={`/admin/orders/${order.id}`}
                              className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gold/10 hover:text-gold"
                            >
                              <Eye className="w-4 h-4 mr-2" />
                              View Details
                            </Link>
                            <Link
                              href={`/admin/orders/${order.id}/edit`}
                              className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gold/10 hover:text-gold"
                            >
                              <Edit className="w-4 h-4 mr-2" />
                              Edit Order
                            </Link>
                            {nextStatus && nextStatus !== order.status && (
                              <button
                                onClick={() => handleStatusChange(order.id, nextStatus)}
                                className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gold/10 hover:text-gold"
                              >
                                <StatusIcon className="w-4 h-4 mr-2" />
                                {getStatusLabel(order.status)}
                              </button>
                            )}
                            {order.status !== 'cancelled' && (
                              <button
                                onClick={() => handleStatusChange(order.id, 'cancelled')}
                                className="flex items-center w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-red-900/10"
                              >
                                <X className="w-4 h-4 mr-2" />
                                Cancel Order
                              </button>
                            )}
                            <button
                              onClick={() => handleDelete(order.id, order.id.slice(0, 8))}
                              className="flex items-center w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-red-900/10"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete Order
                            </button>
                          </div>
                        </div>
                      )}
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}