// List users script
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function listUsers() {
  try {
    console.log('👥 Listing users...\n');
    
    // Get users from our users table
    const { data: users, error } = await supabase
      .from('users')
      .select(`
        id,
        email,
        role,
        email_verified,
        created_at,
        profile:profiles(first_name, last_name)
      `);
    
    if (error) {
      console.error('❌ Error fetching users:', error.message);
      return;
    }
    
    if (!users || users.length === 0) {
      console.log('No users found in the database.');
      return;
    }
    
    console.log('📋 Users in database:');
    console.log('─'.repeat(80));
    
    users.forEach((user, index) => {
      const profile = user.profile?.[0] || {};
      const name = profile.first_name && profile.last_name 
        ? `${profile.first_name} ${profile.last_name}`
        : 'No name';
      
      console.log(`${index + 1}. ${user.email}`);
      console.log(`   ID: ${user.id}`);
      console.log(`   Name: ${name}`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Verified: ${user.email_verified}`);
      console.log(`   Created: ${new Date(user.created_at).toLocaleDateString()}`);
      console.log('');
    });
    
    // Show admin users
    const adminUsers = users.filter(u => u.role === 'admin');
    console.log(`👑 Admin users: ${adminUsers.length}`);
    adminUsers.forEach(admin => {
      console.log(`   - ${admin.email} (${admin.id})`);
    });
    
  } catch (error) {
    console.error('❌ Error listing users:', error);
  }
}

listUsers();