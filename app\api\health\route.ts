// Health check API endpoint
import { NextRequest, NextResponse } from 'next/server';
import { getHealthMetrics } from '@/lib/performance/monitoring';
import { cacheStats } from '@/lib/performance/cache';
import { createClient } from '@/lib/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const startTime = Date.now();
    
    // Check database connectivity
    const supabase = createClient();
    const { error: dbError } = await supabase
      .from('users')
      .select('id')
      .limit(1);
    
    const dbStatus = dbError ? 'unhealthy' : 'healthy';
    const dbResponseTime = Date.now() - startTime;
    
    // Get performance metrics
    const performanceMetrics = getHealthMetrics();
    const cacheMetrics = cacheStats.getStats();
    
    // Determine overall health status
    const isHealthy = dbStatus === 'healthy' && 
                     performanceMetrics.memory.currentMB < 1000 && // Less than 1GB
                     performanceMetrics.api.errorRate < 0.1; // Less than 10% error rate
    
    const healthData = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      
      services: {
        database: {
          status: dbStatus,
          responseTime: dbResponseTime,
          error: dbError?.message,
        },
        cache: {
          status: 'healthy',
          apiCacheSize: cacheMetrics.apiCache.size,
          dataCacheSize: cacheMetrics.dataCache.size,
        },
      },
      
      performance: performanceMetrics,
      
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
      },
    };
    
    const statusCode = isHealthy ? 200 : 503;
    return NextResponse.json(healthData, { status: statusCode });
    
  } catch (error) {
    console.error('Health check error:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 503 });
  }
}