import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    console.log('Starting seed and test process...');
    
    const supabase = createClient();
    
    // First, ensure we have basic users
    const { error: userError } = await supabase
      .from('users')
      .upsert([
        {
          id: '00000000-0000-0000-0000-000000000001',
          email: '<EMAIL>',
          role: 'admin',
          email_verified: true
        },
        {
          id: '00000000-0000-0000-0000-000000000002',
          email: '<EMAIL>',
          role: 'customer',
          email_verified: true
        }
      ], { onConflict: 'email' });
    
    if (userError) {
      console.error('User creation error:', userError);
    }
    
    // Create basic categories if they don't exist
    const { error: categoryError } = await supabase
      .from('categories')
      .upsert([
        {
          name: 'Electronics',
          slug: 'electronics',
          description: 'Electronic devices and gadgets',
          is_active: true,
          sort_order: 1
        },
        {
          name: 'Clothing',
          slug: 'clothing',
          description: 'Fashion and apparel',
          is_active: true,
          sort_order: 2
        }
      ], { onConflict: 'slug' });
    
    if (categoryError) {
      console.error('Category creation error:', categoryError);
    }
    
    // Get category IDs
    const { data: categories } = await supabase
      .from('categories')
      .select('id, slug')
      .limit(2);
    
    if (categories && categories.length > 0) {
      // Create basic products
      const { error: productError } = await supabase
        .from('products')
        .upsert([
          {
            name: 'Wireless Headphones',
            slug: 'wireless-headphones',
            description: 'High-quality wireless headphones with noise cancellation',
            short_description: 'Premium wireless headphones',
            price: 199.99,
            category_id: categories[0].id,
            inventory_count: 5, // Low stock for testing
            status: 'active',
            featured: true
          },
          {
            name: 'Smartphone',
            slug: 'smartphone',
            description: 'Latest model smartphone with advanced features',
            short_description: 'Advanced smartphone',
            price: 699.99,
            category_id: categories[0].id,
            inventory_count: 25,
            status: 'active',
            featured: true
          },
          {
            name: 'T-Shirt',
            slug: 't-shirt',
            description: 'Comfortable cotton t-shirt in various colors',
            short_description: 'Cotton t-shirt',
            price: 29.99,
            category_id: categories[1]?.id || categories[0].id,
            inventory_count: 100,
            status: 'active',
            featured: false
          }
        ], { onConflict: 'slug' });
      
      if (productError) {
        console.error('Product creation error:', productError);
      }
    }
    
    // Create sample orders
    const customerId = '00000000-0000-0000-0000-000000000002';
    const { data: products } = await supabase
      .from('products')
      .select('id, name, price')
      .eq('status', 'active')
      .limit(3);
    
    let ordersCreated = 0;
    
    if (products && products.length > 0) {
      // Create 10 sample orders with different dates
      for (let i = 1; i <= 10; i++) {
        const orderNumber = `JOO${new Date().toISOString().slice(0, 10).replace(/-/g, '')}${String(i).padStart(4, '0')}`;
        const subtotal = Math.round((Math.random() * 500 + 50) * 100) / 100;
        const taxAmount = Math.round(subtotal * 0.1 * 100) / 100;
        const shippingAmount = 9.99;
        const totalAmount = subtotal + taxAmount + shippingAmount;
        
        // Create orders with dates spread over the last 30 days
        const daysAgo = Math.floor(Math.random() * 30);
        const orderDate = new Date(Date.now() - (daysAgo * 24 * 60 * 60 * 1000));
        
        const { data: order, error: orderError } = await supabase
          .from('orders')
          .insert({
            order_number: orderNumber,
            user_id: customerId,
            user_email: '<EMAIL>',
            status: i <= 2 ? 'pending' : i <= 4 ? 'processing' : i <= 7 ? 'shipped' : 'delivered',
            subtotal,
            tax_amount: taxAmount,
            shipping_amount: shippingAmount,
            total_amount: totalAmount,
            shipping_address: {
              first_name: 'John',
              last_name: 'Doe',
              address_line_1: '123 Main St',
              city: 'New York',
              state: 'NY',
              postal_code: '10001',
              country: 'US'
            },
            billing_address: {
              first_name: 'John',
              last_name: 'Doe',
              address_line_1: '123 Main St',
              city: 'New York',
              state: 'NY',
              postal_code: '10001',
              country: 'US'
            },
            payment_method: {
              type: 'credit_card',
              last_four: '4242',
              brand: 'visa'
            },
            created_at: orderDate.toISOString(),
            updated_at: orderDate.toISOString()
          })
          .select()
          .single();
        
        if (orderError) {
          console.error(`Order ${i} creation error:`, orderError);
          continue;
        }
        
        ordersCreated++;
        
        // Add 1-3 order items per order
        if (order) {
          const itemCount = Math.floor(Math.random() * 3) + 1;
          for (let j = 0; j < itemCount; j++) {
            const product = products[j % products.length];
            const quantity = Math.floor(Math.random() * 3) + 1;
            const unitPrice = product.price;
            
            const { error: itemError } = await supabase
              .from('order_items')
              .insert({
                order_id: order.id,
                product_id: product.id,
                quantity,
                unit_price: unitPrice,
                total_price: quantity * unitPrice
              });
            
            if (itemError) {
              console.error(`Order item ${i}-${j} creation error:`, itemError);
            }
          }
        }
      }
    }
    
    // Now test the dashboard functions
    console.log('Testing dashboard functions...');
    
    // Test sales analytics
    const { data: salesData, error: salesError } = await supabase.rpc('get_sales_analytics');
    console.log('Sales analytics result:', { salesData, salesError });
    
    // Test low stock products
    const { data: lowStockData, error: lowStockError } = await supabase.rpc('get_low_stock_products', { threshold: 10 });
    console.log('Low stock result:', { lowStockData, lowStockError });
    
    // Test recent orders
    const { data: ordersData, error: ordersError } = await supabase
      .from('orders')
      .select(`
        *,
        items:order_items(*),
        user:users(*)
      `)
      .order('created_at', { ascending: false })
      .limit(10);
    console.log('Recent orders result:', { count: ordersData?.length || 0, ordersError });
    
    return NextResponse.json({
      success: true,
      message: 'Sample data created and dashboard tested successfully',
      data: {
        ordersCreated,
        salesAnalytics: {
          data: salesData,
          error: salesError?.message || null
        },
        lowStockProducts: {
          count: lowStockData?.length || 0,
          error: lowStockError?.message || null
        },
        recentOrders: {
          count: ordersData?.length || 0,
          error: ordersError?.message || null
        }
      }
    });
    
  } catch (error) {
    console.error('Seed and test error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to seed data and test dashboard', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Use POST method to seed data and test dashboard'
  });
}
