import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function GET(request: NextRequest) {
  try {
    const token = await getToken({ 
      req: request, 
      secret: process.env.NEXTAUTH_SECRET 
    });

    return NextResponse.json({
      success: true,
      hasToken: !!token,
      token: token ? {
        sub: token.sub,
        email: token.email,
        role: token.role,
        emailVerified: token.emailVerified
      } : null
    });
  } catch (error) {
    console.error('Session test error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to get session',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}