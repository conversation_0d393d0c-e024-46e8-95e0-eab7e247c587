#!/usr/bin/env node

/**
 * Fixed Admin User Creation Script for JOOKA E-commerce Platform
 * This script creates an admin user account with simplified input handling
 */

const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

// Try to load dotenv
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function createAdminUser() {
  try {
    console.log('🔐 JOOKA E-commerce Admin User Creation (Fixed Version)\n');
    
    // Get admin details with simplified input
    const email = await question('Enter admin email: ');
    const firstName = await question('Enter first name: ');
    const lastName = await question('Enter last name: ');
    const password = await question('Enter password (min 8 chars): ');
    
    rl.close();
    
    // Validate input
    if (!email || !firstName || !lastName || !password) {
      throw new Error('All fields are required');
    }
    
    if (password.length < 8) {
      throw new Error('Password must be at least 8 characters long');
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format');
    }
    
    console.log('\n🔄 Creating admin user...');
    
    // Check if user already exists in auth
    const { data: existingAuthUser } = await supabase.auth.admin.getUserByEmail(email);
    
    if (existingAuthUser.user) {
      console.log('👤 Auth user already exists, checking database record...');
      
      // Check if user exists in our users table
      const { data: existingUser } = await supabase
        .from('users')
        .select('id, role')
        .eq('email', email)
        .single();
      
      if (existingUser) {
        if (existingUser.role === 'admin') {
          console.log('✅ User already exists and is an admin');
          return;
        } else {
          // Promote existing user to admin
          console.log('👤 User exists as customer, promoting to admin...');
          
          const { error: updateError } = await supabase
            .from('users')
            .update({ role: 'admin' })
            .eq('id', existingUser.id);
          
          if (updateError) {
            throw new Error(`Failed to promote user to admin: ${updateError.message}`);
          }
          
          console.log('✅ User successfully promoted to admin');
          return;
        }
      } else {
        // Auth user exists but no database record - create it
        console.log('🔄 Creating database record for existing auth user...');
        
        const { error: userError } = await supabase
          .from('users')
          .insert({
            id: existingAuthUser.user.id,
            email: email,
            email_verified: true,
            role: 'admin',
          });
        
        if (userError) {
          console.error('Detailed user creation error:', userError);
          throw new Error(`Failed to create user record: ${userError.message || 'Unknown error'}`);
        }
        
        console.log('✅ Database user record created successfully');
        
        // Create profile record
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            user_id: existingAuthUser.user.id,
            first_name: firstName,
            last_name: lastName,
          });
        
        if (profileError) {
          console.warn('⚠️  Warning creating profile:', profileError.message);
        } else {
          console.log('✅ Profile created successfully');
        }
        
        console.log('\n🎉 Admin user setup completed successfully!');
        console.log(`📧 Email: ${email}`);
        console.log(`👤 Name: ${firstName} ${lastName}`);
        console.log(`🔑 Role: admin`);
        return;
      }
    }
    
    // Create new admin user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm admin users
      user_metadata: {
        first_name: firstName,
        last_name: lastName,
      },
    });
    
    if (authError) {
      throw new Error(`Failed to create auth user: ${authError.message}`);
    }
    
    if (!authData.user) {
      throw new Error('Failed to create user account');
    }
    
    console.log('✅ Auth user created successfully');
    
    // Create user record with admin role
    const { error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: email,
        email_verified: true,
        role: 'admin',
      });
    
    if (userError) {
      console.error('Detailed user creation error:', userError);
      throw new Error(`Failed to create user record: ${userError.message || 'Unknown error'}`);
    }
    
    console.log('✅ User record created successfully');
    
    // Create profile record
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        user_id: authData.user.id,
        first_name: firstName,
        last_name: lastName,
      });
    
    if (profileError) {
      console.warn('⚠️  Warning creating profile:', profileError.message);
    } else {
      console.log('✅ Profile created successfully');
    }
    
    console.log('\n🎉 Admin user created successfully!');
    console.log(`📧 Email: ${email}`);
    console.log(`👤 Name: ${firstName} ${lastName}`);
    console.log(`🔑 Role: admin`);
    console.log('\n📋 Next steps:');
    console.log('1. The admin can now sign in at /auth/signin');
    console.log('2. They will be redirected to /admin/dashboard after login');
    console.log('3. Admin has full access to all admin features');
    
  } catch (error) {
    console.error('\n❌ Failed to create admin user:', error.message);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Admin creation cancelled');
  rl.close();
  process.exit(0);
});

// Run the script
if (require.main === module) {
  createAdminUser();
}

module.exports = { createAdminUser };