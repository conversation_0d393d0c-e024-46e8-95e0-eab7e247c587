import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    console.log('Checking database structure...');
    
    // Check if tables exist
    const tables = ['users', 'categories', 'products', 'orders', 'order_items'];
    const tableChecks = {};
    
    for (const table of tables) {
      try {
        const { data, error, count } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        tableChecks[table] = {
          exists: !error,
          count: count || 0,
          error: error?.message || null
        };
      } catch (err) {
        tableChecks[table] = {
          exists: false,
          count: 0,
          error: err instanceof Error ? err.message : 'Unknown error'
        };
      }
    }
    
    // Check specific data
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, role')
      .limit(5);
    
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('id, name, slug')
      .limit(5);
    
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, name, slug, status, inventory_count')
      .limit(5);
    
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select('id, order_number, status, total_amount, created_at')
      .limit(5);
    
    // Test database functions
    const { data: salesData, error: salesError } = await supabase.rpc('get_sales_analytics');
    const { data: lowStockData, error: lowStockError } = await supabase.rpc('get_low_stock_products', { threshold: 10 });
    
    return NextResponse.json({
      success: true,
      data: {
        tableChecks,
        sampleData: {
          users: {
            data: users || [],
            error: usersError?.message || null
          },
          categories: {
            data: categories || [],
            error: categoriesError?.message || null
          },
          products: {
            data: products || [],
            error: productsError?.message || null
          },
          orders: {
            data: orders || [],
            error: ordersError?.message || null
          }
        },
        functions: {
          salesAnalytics: {
            data: salesData,
            error: salesError?.message || null
          },
          lowStockProducts: {
            data: lowStockData || [],
            error: lowStockError?.message || null
          }
        }
      }
    });
    
  } catch (error) {
    console.error('Database check error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check database', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
