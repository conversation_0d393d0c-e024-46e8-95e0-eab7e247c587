// Check database status script
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkDatabaseStatus() {
  console.log('🔍 Checking JOOKA E-commerce database status...\n');
  
  const tables = ['users', 'profiles', 'categories', 'products', 'orders', 'cart_items'];
  const functions = ['get_sales_analytics', 'get_low_stock_products', 'create_order_with_items'];
  
  // Check tables
  console.log('📊 Checking tables:');
  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('count')
        .limit(1);
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`);
      } else {
        console.log(`✅ ${table}: Table exists`);
      }
    } catch (err) {
      console.log(`❌ ${table}: ${err.message}`);
    }
  }
  
  console.log('\n🔧 Checking functions:');
  for (const func of functions) {
    try {
      const { data, error } = await supabase.rpc(func);
      
      if (error) {
        console.log(`❌ ${func}: ${error.message}`);
      } else {
        console.log(`✅ ${func}: Function exists`);
      }
    } catch (err) {
      console.log(`❌ ${func}: ${err.message}`);
    }
  }
  
  // Check for sample data
  console.log('\n📦 Checking sample data:');
  try {
    const { data: categories, error: catError } = await supabase
      .from('categories')
      .select('count');
    
    if (!catError && categories) {
      console.log(`✅ Categories: ${categories.length} records`);
    } else {
      console.log(`❌ Categories: No data or error`);
    }
    
    const { data: products, error: prodError } = await supabase
      .from('products')
      .select('count');
    
    if (!prodError && products) {
      console.log(`✅ Products: ${products.length} records`);
    } else {
      console.log(`❌ Products: No data or error`);
    }
  } catch (err) {
    console.log(`❌ Sample data check failed: ${err.message}`);
  }
  
  console.log('\n📋 Summary:');
  console.log('If you see ❌ errors above, you need to:');
  console.log('1. Run the schema SQL in Supabase SQL Editor');
  console.log('2. Run the functions SQL in Supabase SQL Editor');
  console.log('3. Run the seed data SQL in Supabase SQL Editor');
  console.log('\nSee DASHBOARD_SETUP_GUIDE.md for detailed instructions.');
}

checkDatabaseStatus().catch(console.error);