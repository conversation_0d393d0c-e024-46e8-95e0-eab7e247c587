// Products API routes
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';
import { db } from '@/lib/database/index';
import { z } from 'zod';

const createProductSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  slug: z.string().min(1, 'Product slug is required'),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  price: z.number().min(0, 'Price must be positive'),
  comparePrice: z.number().min(0).optional(),
  costPrice: z.number().min(0).optional(),
  categoryId: z.string().optional(),
  inventoryCount: z.number().min(0).default(0),
  trackInventory: z.boolean().default(true),
  allowBackorder: z.boolean().default(false),
  weight: z.number().min(0).optional(),
  dimensions: z.object({
    length: z.number().min(0),
    width: z.number().min(0),
    height: z.number().min(0),
  }).optional(),
  images: z.array(z.object({
    id: z.string(),
    secure_url: z.string(),
    alt_text: z.string(),
    is_primary: z.boolean(),
    order: z.number(),
  })).default([]),
  tags: z.array(z.string()).default([]),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  status: z.enum(['active', 'inactive', 'out_of_stock']).default('active'),
  featured: z.boolean().default(false),
});

async function getProducts(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId') || undefined;
    const status = searchParams.get('status') || undefined;
    const featured = searchParams.get('featured') === 'true' ? true : undefined;
    const search = searchParams.get('search') || undefined;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    const result = await db.getProducts({
      categoryId,
      status,
      featured,
      search,
      page,
      limit
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Get products error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

async function createProduct(request: NextRequest) {
  try {
    const body = await request.json();
    const validationResult = createProductSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const productData = {
      name: validationResult.data.name,
      slug: validationResult.data.slug,
      description: validationResult.data.description || null,
      short_description: validationResult.data.shortDescription || null,
      price: validationResult.data.price,
      compare_price: validationResult.data.comparePrice || null,
      cost_price: validationResult.data.costPrice || null,
      category_id: validationResult.data.categoryId || null,
      inventory_count: validationResult.data.inventoryCount,
      track_inventory: validationResult.data.trackInventory,
      allow_backorder: validationResult.data.allowBackorder,
      weight: validationResult.data.weight || null,
      dimensions: validationResult.data.dimensions || null,
      images: validationResult.data.images,
      tags: validationResult.data.tags,
      meta_title: validationResult.data.metaTitle || null,
      meta_description: validationResult.data.metaDescription || null,
      status: validationResult.data.status,
      featured: validationResult.data.featured,
    };

    const result = await db.createProduct(productData);

    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Product created successfully',
      data: result.data
    }, { status: 201 });
  } catch (error) {
    console.error('Create product error:', error);
    return NextResponse.json(
      { error: 'Failed to create product' },
      { status: 500 }
    );
  }
}

export const GET = getProducts; // Public endpoint
export const POST = withAuth(createProduct, { requireAdmin: true });