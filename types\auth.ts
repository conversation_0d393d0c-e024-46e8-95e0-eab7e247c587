export interface User {
  id: string
  email: string
  role: 'admin' | 'customer'
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
  profile: UserProfile
}

export interface UserProfile {
  id: string
  userId: string
  firstName: string
  lastName: string
  phone?: string
  avatarUrl?: string
  addresses: Address[]
  paymentMethods: PaymentMethod[]
}

export interface Address {
  id: string
  type: 'shipping' | 'billing'
  street: string
  city: string
  state: string
  zipCode: string
  country: string
  isDefault: boolean
}

export interface PaymentMethod {
  id: string
  type: 'card' | 'paypal'
  last4?: string
  brand?: string
  expiryMonth?: number
  expiryYear?: number
  isDefault: boolean
  token: string // Tokenized payment method
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

// Extend NextAuth types
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name?: string | null;
      image?: string | null;
      role: string;
      emailVerified: boolean;
    };
  }

  interface User {
    id: string;
    email: string;
    name?: string | null;
    image?: string | null;
    role?: string;
    emailVerified?: Date | null;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: string;
    emailVerified: boolean;
  }
}