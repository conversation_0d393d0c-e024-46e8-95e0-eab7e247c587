// User payment methods management API routes
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';
import { db } from '@/lib/database';
import { z } from 'zod';

const createPaymentMethodSchema = z.object({
  type: z.string().min(1, 'Payment method type is required'),
  provider: z.string().min(1, 'Provider is required'),
  providerPaymentMethodId: z.string().min(1, 'Provider payment method ID is required'),
  lastFour: z.string().optional(),
  brand: z.string().optional(),
  expMonth: z.number().min(1).max(12).optional(),
  expYear: z.number().min(new Date().getFullYear()).optional(),
  isDefault: z.boolean().default(false),
});

async function getPaymentMethods(request: NextRequest) {
  const user = (request as any).user;
  
  try {
    const result = await db.getUserPaymentMethods(user.id);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: result.data });
  } catch (error) {
    console.error('Get payment methods error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payment methods' },
      { status: 500 }
    );
  }
}

async function createPaymentMethod(request: NextRequest) {
  const user = (request as any).user;
  
  try {
    const body = await request.json();
    const validationResult = createPaymentMethodSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const paymentMethodData = {
      user_id: user.id,
      type: validationResult.data.type,
      provider: validationResult.data.provider,
      provider_payment_method_id: validationResult.data.providerPaymentMethodId,
      last_four: validationResult.data.lastFour,
      brand: validationResult.data.brand,
      exp_month: validationResult.data.expMonth,
      exp_year: validationResult.data.expYear,
      is_default: validationResult.data.isDefault,
    };

    const result = await db.createPaymentMethod(paymentMethodData);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      message: 'Payment method created successfully',
      data: result.data 
    }, { status: 201 });
  } catch (error) {
    console.error('Create payment method error:', error);
    return NextResponse.json(
      { error: 'Failed to create payment method' },
      { status: 500 }
    );
  }
}

export const GET = withAuth(getPaymentMethods);
export const POST = withAuth(createPaymentMethod);