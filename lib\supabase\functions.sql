-- Database Functions for JOOKA E-commerce Platform
-- This file contains utility functions for common operations

-- Function to generate unique order numbers
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    order_num TEXT;
    exists_check BOOLEAN;
BEGIN
    LOOP
        -- Generate order number: JOO + timestamp + random 4 digits
        order_num := 'JOO' || TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
        
        -- Check if this order number already exists
        SELECT EXISTS(SELECT 1 FROM public.orders WHERE order_number = order_num) INTO exists_check;
        
        -- If it doesn't exist, we can use it
        IF NOT exists_check THEN
            EXIT;
        END IF;
    END LOOP;
    
    RETURN order_num;
END;
$$ LANGUAGE plpgsql;

-- Function to update product inventory
CREATE OR REPLACE FUNCTION update_product_inventory(
    product_uuid UUID,
    quantity_change INTEGER,
    transaction_type VARCHAR(50),
    reference_uuid UUID DEFAULT NULL,
    reference_type VARCHAR(50) DEFAULT NULL,
    notes TEXT DEFAULT NULL,
    user_uuid UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_inventory INTEGER;
    new_inventory INTEGER;
BEGIN
    -- Get current inventory
    SELECT inventory_count INTO current_inventory
    FROM public.products
    WHERE id = product_uuid;
    
    IF current_inventory IS NULL THEN
        RAISE EXCEPTION 'Product not found';
    END IF;
    
    -- Calculate new inventory
    new_inventory := current_inventory + quantity_change;
    
    -- Check if new inventory would be negative
    IF new_inventory < 0 THEN
        RAISE EXCEPTION 'Insufficient inventory. Current: %, Requested change: %', current_inventory, quantity_change;
    END IF;
    
    -- Update product inventory
    UPDATE public.products
    SET 
        inventory_count = new_inventory,
        updated_at = NOW()
    WHERE id = product_uuid;
    
    -- Create inventory transaction record
    INSERT INTO public.inventory_transactions (
        product_id,
        type,
        quantity_change,
        quantity_after,
        reference_id,
        reference_type,
        notes,
        created_by
    ) VALUES (
        product_uuid,
        transaction_type,
        quantity_change,
        new_inventory,
        reference_uuid,
        reference_type,
        notes,
        user_uuid
    );
    
    -- Update product status based on inventory
    UPDATE public.products
    SET status = CASE
        WHEN new_inventory = 0 THEN 'out_of_stock'::product_status
        WHEN status = 'out_of_stock' AND new_inventory > 0 THEN 'active'::product_status
        ELSE status
    END
    WHERE id = product_uuid;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate order totals
CREATE OR REPLACE FUNCTION calculate_order_totals(order_uuid UUID)
RETURNS JSONB AS $$
DECLARE
    subtotal DECIMAL(10,2);
    tax_rate DECIMAL(5,4) := 0.0875; -- 8.75% default tax rate
    tax_amount DECIMAL(10,2);
    shipping_amount DECIMAL(10,2) := 0.00; -- Free shipping for now
    discount_amount DECIMAL(10,2) := 0.00; -- No discounts for now
    total_amount DECIMAL(10,2);
    result JSONB;
BEGIN
    -- Calculate subtotal from order items
    SELECT COALESCE(SUM(total_price), 0) INTO subtotal
    FROM public.order_items
    WHERE order_id = order_uuid;
    
    -- Calculate tax (simplified - in real app, would be based on shipping address)
    tax_amount := ROUND(subtotal * tax_rate, 2);
    
    -- Calculate total
    total_amount := subtotal + tax_amount + shipping_amount - discount_amount;
    
    -- Build result object
    result := jsonb_build_object(
        'subtotal', subtotal,
        'tax_amount', tax_amount,
        'shipping_amount', shipping_amount,
        'discount_amount', discount_amount,
        'total_amount', total_amount
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to create order with items
CREATE OR REPLACE FUNCTION create_order_with_items(
    user_uuid UUID,
    user_email VARCHAR(255),
    shipping_address JSONB,
    billing_address JSONB,
    payment_method JSONB,
    cart_items JSONB -- Array of {product_id, quantity}
)
RETURNS UUID AS $$
DECLARE
    order_uuid UUID;
    order_num TEXT;
    item JSONB;
    product_record RECORD;
    totals JSONB;
BEGIN
    -- Generate order number
    order_num := generate_order_number();
    
    -- Create order
    INSERT INTO public.orders (
        order_number,
        user_id,
        email,
        shipping_address,
        billing_address,
        payment_method,
        subtotal,
        tax_amount,
        shipping_amount,
        discount_amount,
        total_amount
    ) VALUES (
        order_num,
        user_uuid,
        user_email,
        shipping_address,
        billing_address,
        payment_method,
        0, 0, 0, 0, 0 -- Will be updated after items are added
    ) RETURNING id INTO order_uuid;
    
    -- Add order items
    FOR item IN SELECT * FROM jsonb_array_elements(cart_items)
    LOOP
        -- Get product details
        SELECT * INTO product_record
        FROM public.products
        WHERE id = (item->>'product_id')::UUID
        AND status = 'active';
        
        IF product_record IS NULL THEN
            RAISE EXCEPTION 'Product not found or inactive: %', item->>'product_id';
        END IF;
        
        -- Check inventory
        IF product_record.track_inventory AND 
           product_record.inventory_count < (item->>'quantity')::INTEGER THEN
            RAISE EXCEPTION 'Insufficient inventory for product: %. Available: %, Requested: %', 
                product_record.name, product_record.inventory_count, item->>'quantity';
        END IF;
        
        -- Create order item
        INSERT INTO public.order_items (
            order_id,
            product_id,
            product_name,
            product_slug,
            quantity,
            unit_price,
            total_price,
            product_snapshot
        ) VALUES (
            order_uuid,
            product_record.id,
            product_record.name,
            product_record.slug,
            (item->>'quantity')::INTEGER,
            product_record.price,
            product_record.price * (item->>'quantity')::INTEGER,
            to_jsonb(product_record)
        );
        
        -- Update inventory if tracking is enabled
        IF product_record.track_inventory THEN
            PERFORM update_product_inventory(
                product_record.id,
                -(item->>'quantity')::INTEGER,
                'sale',
                order_uuid,
                'order',
                'Order: ' || order_num,
                user_uuid
            );
        END IF;
    END LOOP;
    
    -- Calculate and update order totals
    totals := calculate_order_totals(order_uuid);
    
    UPDATE public.orders
    SET
        subtotal = (totals->>'subtotal')::DECIMAL(10,2),
        tax_amount = (totals->>'tax_amount')::DECIMAL(10,2),
        shipping_amount = (totals->>'shipping_amount')::DECIMAL(10,2),
        discount_amount = (totals->>'discount_amount')::DECIMAL(10,2),
        total_amount = (totals->>'total_amount')::DECIMAL(10,2),
        updated_at = NOW()
    WHERE id = order_uuid;
    
    -- Clear user's cart
    DELETE FROM public.cart_items WHERE user_id = user_uuid;
    
    RETURN order_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update order status
CREATE OR REPLACE FUNCTION update_order_status(
    order_uuid UUID,
    new_status order_status,
    admin_notes TEXT DEFAULT NULL,
    tracking_number VARCHAR(255) DEFAULT NULL,
    tracking_url TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    old_status order_status;
    user_uuid UUID;
    order_num VARCHAR(50);
BEGIN
    -- Get current status and user
    SELECT status, user_id, order_number 
    INTO old_status, user_uuid, order_num
    FROM public.orders 
    WHERE id = order_uuid;
    
    IF old_status IS NULL THEN
        RAISE EXCEPTION 'Order not found';
    END IF;
    
    -- Update order
    UPDATE public.orders
    SET
        status = new_status,
        admin_notes = COALESCE(admin_notes, admin_notes),
        tracking_number = COALESCE(tracking_number, tracking_number),
        tracking_url = COALESCE(tracking_url, tracking_url),
        shipped_at = CASE WHEN new_status = 'shipped' THEN NOW() ELSE shipped_at END,
        delivered_at = CASE WHEN new_status = 'delivered' THEN NOW() ELSE delivered_at END,
        updated_at = NOW()
    WHERE id = order_uuid;
    
    -- Create notification for customer
    IF user_uuid IS NOT NULL THEN
        INSERT INTO public.notifications (
            user_id,
            type,
            title,
            message,
            data
        ) VALUES (
            user_uuid,
            'order_status',
            'Order Status Update',
            'Your order ' || order_num || ' status has been updated to ' || new_status,
            jsonb_build_object(
                'order_id', order_uuid,
                'order_number', order_num,
                'old_status', old_status,
                'new_status', new_status,
                'tracking_number', tracking_number
            )
        );
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get low stock products
CREATE OR REPLACE FUNCTION get_low_stock_products(threshold INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    name VARCHAR(255),
    inventory_count INTEGER,
    category_name VARCHAR(100)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.inventory_count,
        c.name as category_name
    FROM public.products p
    LEFT JOIN public.categories c ON p.category_id = c.id
    WHERE p.track_inventory = true
    AND p.inventory_count <= threshold
    AND p.status = 'active'
    ORDER BY p.inventory_count ASC, p.name ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get sales analytics
CREATE OR REPLACE FUNCTION get_sales_analytics(
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS JSONB AS $$
DECLARE
    total_orders INTEGER;
    total_revenue DECIMAL(10,2);
    avg_order_value DECIMAL(10,2);
    top_products JSONB;
    daily_sales JSONB;
    result JSONB;
BEGIN
    -- Get total orders and revenue
    SELECT 
        COUNT(*),
        COALESCE(SUM(total_amount), 0),
        COALESCE(AVG(total_amount), 0)
    INTO total_orders, total_revenue, avg_order_value
    FROM public.orders
    WHERE created_at::DATE BETWEEN start_date AND end_date
    AND status != 'cancelled';
    
    -- Get top products
    SELECT jsonb_agg(
        jsonb_build_object(
            'product_name', product_name,
            'quantity_sold', quantity_sold,
            'revenue', revenue
        )
    ) INTO top_products
    FROM (
        SELECT 
            oi.product_name,
            SUM(oi.quantity) as quantity_sold,
            SUM(oi.total_price) as revenue
        FROM public.order_items oi
        JOIN public.orders o ON oi.order_id = o.id
        WHERE o.created_at::DATE BETWEEN start_date AND end_date
        AND o.status != 'cancelled'
        GROUP BY oi.product_name
        ORDER BY quantity_sold DESC
        LIMIT 10
    ) top_products_query;
    
    -- Get daily sales
    SELECT jsonb_agg(
        jsonb_build_object(
            'date', sale_date,
            'orders', order_count,
            'revenue', daily_revenue
        ) ORDER BY sale_date
    ) INTO daily_sales
    FROM (
        SELECT 
            created_at::DATE as sale_date,
            COUNT(*) as order_count,
            SUM(total_amount) as daily_revenue
        FROM public.orders
        WHERE created_at::DATE BETWEEN start_date AND end_date
        AND status != 'cancelled'
        GROUP BY created_at::DATE
        ORDER BY created_at::DATE
    ) daily_sales_query;
    
    -- Build result
    result := jsonb_build_object(
        'period', jsonb_build_object(
            'start_date', start_date,
            'end_date', end_date
        ),
        'summary', jsonb_build_object(
            'total_orders', total_orders,
            'total_revenue', total_revenue,
            'avg_order_value', avg_order_value
        ),
        'top_products', COALESCE(top_products, '[]'::jsonb),
        'daily_sales', COALESCE(daily_sales, '[]'::jsonb)
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;