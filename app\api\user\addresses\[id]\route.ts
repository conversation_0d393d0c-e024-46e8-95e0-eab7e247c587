// Individual address management API routes
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';
import { db } from '@/lib/database';
import { z } from 'zod';

const updateAddressSchema = z.object({
  type: z.enum(['shipping', 'billing']).optional(),
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  company: z.string().optional(),
  streetAddress1: z.string().min(1, 'Street address is required').optional(),
  streetAddress2: z.string().optional(),
  city: z.string().min(1, 'City is required').optional(),
  state: z.string().min(1, 'State is required').optional(),
  postalCode: z.string().min(1, 'Postal code is required').optional(),
  country: z.string().optional(),
  phone: z.string().optional(),
  isDefault: z.boolean().optional(),
});

async function updateAddress(request: NextRequest, { params }: { params: { id: string } }) {
  const user = (request as any).user;
  const addressId = params.id;
  
  try {
    const body = await request.json();
    const validationResult = updateAddressSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const updateData: any = {};
    if (validationResult.data.type) updateData.type = validationResult.data.type;
    if (validationResult.data.firstName) updateData.first_name = validationResult.data.firstName;
    if (validationResult.data.lastName) updateData.last_name = validationResult.data.lastName;
    if (validationResult.data.company !== undefined) updateData.company = validationResult.data.company;
    if (validationResult.data.streetAddress1) updateData.street_address_1 = validationResult.data.streetAddress1;
    if (validationResult.data.streetAddress2 !== undefined) updateData.street_address_2 = validationResult.data.streetAddress2;
    if (validationResult.data.city) updateData.city = validationResult.data.city;
    if (validationResult.data.state) updateData.state = validationResult.data.state;
    if (validationResult.data.postalCode) updateData.postal_code = validationResult.data.postalCode;
    if (validationResult.data.country) updateData.country = validationResult.data.country;
    if (validationResult.data.phone !== undefined) updateData.phone = validationResult.data.phone;
    if (validationResult.data.isDefault !== undefined) updateData.is_default = validationResult.data.isDefault;

    const result = await db.updateAddress(addressId, updateData);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      message: 'Address updated successfully',
      data: result.data 
    });
  } catch (error) {
    console.error('Update address error:', error);
    return NextResponse.json(
      { error: 'Failed to update address' },
      { status: 500 }
    );
  }
}

async function deleteAddress(request: NextRequest, { params }: { params: { id: string } }) {
  const addressId = params.id;
  
  try {
    const result = await db.deleteAddress(addressId);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ message: 'Address deleted successfully' });
  } catch (error) {
    console.error('Delete address error:', error);
    return NextResponse.json(
      { error: 'Failed to delete address' },
      { status: 500 }
    );
  }
}

export const PUT = withAuth(updateAddress);
export const DELETE = withAuth(deleteAddress);