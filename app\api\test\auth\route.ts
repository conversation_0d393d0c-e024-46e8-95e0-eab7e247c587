// Test API route to verify authentication middleware
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';

async function handler(request: NextRequest) {
  const user = (request as any).user;
  
  return NextResponse.json({
    message: 'Authentication successful',
    user: {
      id: user.id,
      email: user.email,
      role: user.role,
      emailVerified: user.emailVerified,
    },
  });
}

export const GET = withAuth(handler);
export const POST = withAuth(handler);