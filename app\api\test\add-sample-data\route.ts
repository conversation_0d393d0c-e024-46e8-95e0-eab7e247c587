import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase/service';

export async function POST(request: NextRequest) {
  try {
    const supabase = createServiceClient();
    
    console.log('Adding sample data step by step...');
    
    // Step 1: Add users
    console.log('Step 1: Adding users...');
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert([
        {
          id: '550e8400-e29b-41d4-a716-446655440001',
          email: '<EMAIL>',
          role: 'admin',
          email_verified: true
        },
        {
          id: '550e8400-e29b-41d4-a716-446655440002',
          email: '<EMAIL>',
          role: 'customer',
          email_verified: true
        }
      ])
      .select();
    
    console.log('Users result:', { userData, userError });
    
    // Step 2: Add profiles
    console.log('Step 2: Adding profiles...');
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .insert([
        {
          user_id: '550e8400-e29b-41d4-a716-446655440001',
          first_name: 'Admin',
          last_name: 'User'
        },
        {
          user_id: '550e8400-e29b-41d4-a716-446655440002',
          first_name: 'John',
          last_name: 'Doe'
        }
      ])
      .select();
    
    console.log('Profiles result:', { profileData, profileError });
    
    // Step 3: Get categories (should already exist)
    console.log('Step 3: Getting categories...');
    const { data: categories, error: catError } = await supabase
      .from('categories')
      .select('id, name, slug')
      .limit(2);
    
    console.log('Categories result:', { categories, catError });
    
    if (!categories || categories.length === 0) {
      console.log('No categories found, creating them...');
      const { data: newCategories, error: newCatError } = await supabase
        .from('categories')
        .insert([
          {
            name: 'Electronics',
            slug: 'electronics',
            description: 'Electronic devices and gadgets',
            is_active: true,
            sort_order: 1
          },
          {
            name: 'Clothing',
            slug: 'clothing',
            description: 'Fashion and apparel',
            is_active: true,
            sort_order: 2
          }
        ])
        .select();
      
      console.log('New categories result:', { newCategories, newCatError });
    }
    
    // Step 4: Add products
    console.log('Step 4: Adding products...');
    const { data: finalCategories } = await supabase
      .from('categories')
      .select('id, slug')
      .limit(2);
    
    if (finalCategories && finalCategories.length > 0) {
      const { data: productData, error: productError } = await supabase
        .from('products')
        .insert([
          {
            name: 'Wireless Headphones',
            slug: 'wireless-headphones-test',
            description: 'High-quality wireless headphones with noise cancellation',
            short_description: 'Premium wireless headphones',
            price: 199.99,
            category_id: finalCategories[0].id,
            inventory_count: 5, // Low stock
            status: 'active',
            featured: true,
            track_inventory: true
          },
          {
            name: 'Smartphone',
            slug: 'smartphone-test',
            description: 'Latest model smartphone with advanced features',
            short_description: 'Advanced smartphone',
            price: 699.99,
            category_id: finalCategories[0].id,
            inventory_count: 25,
            status: 'active',
            featured: true,
            track_inventory: true
          },
          {
            name: 'T-Shirt',
            slug: 't-shirt-test',
            description: 'Comfortable cotton t-shirt in various colors',
            short_description: 'Cotton t-shirt',
            price: 29.99,
            category_id: finalCategories[1]?.id || finalCategories[0].id,
            inventory_count: 100,
            status: 'active',
            featured: false,
            track_inventory: true
          }
        ])
        .select();
      
      console.log('Products result:', { productData, productError });
      
      // Step 5: Add orders
      console.log('Step 5: Adding orders...');
      if (productData && productData.length > 0) {
        const customerId = '550e8400-e29b-41d4-a716-446655440002';
        
        // Create 5 orders with different dates
        for (let i = 1; i <= 5; i++) {
          const orderDate = new Date();
          orderDate.setDate(orderDate.getDate() - i);
          
          const subtotal = 100 + (i * 50);
          const taxAmount = subtotal * 0.1;
          const shippingAmount = 9.99;
          const totalAmount = subtotal + taxAmount + shippingAmount;
          
          const { data: orderData, error: orderError } = await supabase
            .from('orders')
            .insert({
              order_number: `JOO2025073${String(i).padStart(2, '0')}${String(Math.floor(Math.random() * 100)).padStart(2, '0')}`,
              user_id: customerId,
              user_email: '<EMAIL>',
              status: i === 1 ? 'pending' : i === 2 ? 'processing' : 'delivered',
              subtotal: subtotal,
              tax_amount: taxAmount,
              shipping_amount: shippingAmount,
              total_amount: totalAmount,
              shipping_address: {
                first_name: 'John',
                last_name: 'Doe',
                address_line_1: '123 Main St',
                city: 'New York',
                state: 'NY',
                postal_code: '10001',
                country: 'US'
              },
              billing_address: {
                first_name: 'John',
                last_name: 'Doe',
                address_line_1: '123 Main St',
                city: 'New York',
                state: 'NY',
                postal_code: '10001',
                country: 'US'
              },
              payment_method: {
                type: 'credit_card',
                last_four: '4242',
                brand: 'visa'
              },
              created_at: orderDate.toISOString(),
              updated_at: orderDate.toISOString()
            })
            .select()
            .single();
          
          console.log(`Order ${i} result:`, { orderData, orderError });
          
          // Add order items
          if (orderData && !orderError) {
            const { data: itemData, error: itemError } = await supabase
              .from('order_items')
              .insert({
                order_id: orderData.id,
                product_id: productData[i % productData.length].id,
                quantity: 1,
                unit_price: productData[i % productData.length].price,
                total_price: productData[i % productData.length].price
              })
              .select();
            
            console.log(`Order item ${i} result:`, { itemData, itemError });
          }
        }
      }
    }
    
    // Step 6: Test the functions again
    console.log('Step 6: Testing functions...');
    const { data: salesData, error: salesError } = await supabase.rpc('get_sales_analytics');
    const { data: lowStockData, error: lowStockError } = await supabase.rpc('get_low_stock_products', { threshold: 10 });
    
    // Get final counts
    const { count: userCount } = await supabase.from('users').select('*', { count: 'exact', head: true });
    const { count: productCount } = await supabase.from('products').select('*', { count: 'exact', head: true });
    const { count: orderCount } = await supabase.from('orders').select('*', { count: 'exact', head: true });
    
    return NextResponse.json({
      success: true,
      message: 'Sample data added successfully',
      data: {
        counts: {
          users: userCount || 0,
          products: productCount || 0,
          orders: orderCount || 0
        },
        functions: {
          salesAnalytics: {
            data: salesData,
            error: salesError?.message || null
          },
          lowStockProducts: {
            count: lowStockData?.length || 0,
            error: lowStockError?.message || null
          }
        }
      }
    });
    
  } catch (error) {
    console.error('Add sample data error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to add sample data', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
