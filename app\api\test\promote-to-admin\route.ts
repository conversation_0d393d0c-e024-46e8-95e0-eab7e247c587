import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase/service';

export async function POST(request: NextRequest) {
  try {
    const supabase = createServiceClient();
    
    const { email } = await request.json();
    
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }
    
    console.log(`Promoting user ${email} to admin...`);
    
    // Update user role to admin
    const { data: updatedUser, error: updateError } = await supabase
      .from('users')
      .update({ role: 'admin' })
      .eq('email', email)
      .select()
      .single();
    
    if (updateError) {
      console.error('Update error:', updateError);
      return NextResponse.json(
        { error: updateError.message },
        { status: 500 }
      );
    }
    
    console.log('User promoted successfully:', updatedUser);
    
    return NextResponse.json({
      success: true,
      message: `User ${email} has been promoted to admin`,
      data: updatedUser
    });
    
  } catch (error) {
    console.error('Promote to admin error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to promote user to admin', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Use POST method with {"email": "<EMAIL>"} to promote a user to admin'
  });
}
