'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { 
  ShoppingBag, 
  Package, 
  MapPin, 
  Bell,
  TrendingUp,
  Clock
} from 'lucide-react';
import StatsCard from '@/components/dashboard/StatsCard';
import RecentOrdersCustomer from '@/components/dashboard/RecentOrdersCustomer';
import QuickActions from '@/components/dashboard/QuickActions';

interface DashboardData {
  user: any;
  recentOrders: any[];
  cart: {
    items: any[];
    itemCount: number;
  };
  addresses: any[];
  paymentMethods: any[];
  notifications: any[];
  stats: {
    totalOrders: number;
    cartItems: number;
    savedAddresses: number;
    unreadNotifications: number;
  };
}

export default function CustomerDashboard() {
  const { data: session } = useSession();
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/customer/dashboard');
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard data');
      }
      const result = await response.json();
      setData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gold"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-500 rounded-lg p-4">
        <p className="text-red-400">Error: {error}</p>
      </div>
    );
  }

  if (!data) return null;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gold mb-2">
          Welcome back, {session?.user?.name || 'Customer'}!
        </h1>
        <p className="text-gray-400">Here's what's happening with your account.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Orders"
          value={data.stats.totalOrders.toString()}
          icon={ShoppingBag}
          trend={data.stats.totalOrders > 0 ? "Active customer" : "Start shopping"}
          trendUp={data.stats.totalOrders > 0}
        />
        <StatsCard
          title="Cart Items"
          value={data.stats.cartItems.toString()}
          icon={Package}
          trend={data.stats.cartItems > 0 ? "Ready to checkout" : "Cart is empty"}
          trendUp={data.stats.cartItems > 0}
        />
        <StatsCard
          title="Saved Addresses"
          value={data.stats.savedAddresses.toString()}
          icon={MapPin}
          trend={data.stats.savedAddresses > 0 ? "Addresses saved" : "Add address"}
          trendUp={data.stats.savedAddresses > 0}
        />
        <StatsCard
          title="Notifications"
          value={data.stats.unreadNotifications.toString()}
          icon={Bell}
          trend={data.stats.unreadNotifications > 0 ? "Unread messages" : "All caught up"}
          trendUp={false}
        />
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Orders */}
        <div className="lg:col-span-2">
          <RecentOrdersCustomer orders={data.recentOrders} />
        </div>

        {/* Quick Actions */}
        <div>
          <QuickActions 
            cartItemCount={data.stats.cartItems}
            hasAddresses={data.stats.savedAddresses > 0}
            hasPaymentMethods={data.paymentMethods.length > 0}
          />
        </div>
      </div>
    </div>
  );
}