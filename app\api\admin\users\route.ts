// Admin users management API routes
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';
import { db } from '@/lib/database/index';
import { z } from 'zod';

const updateUserRoleSchema = z.object({
  role: z.enum(['admin', 'customer']),
});

const createUserSchema = z.object({
  email: z.string().email('Invalid email address'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  role: z.enum(['admin', 'customer']).default('customer'),
  phone: z.string().optional(),
});

async function getUsers(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || undefined;
    const role = searchParams.get('role') || undefined;

    const result = await db.getUsers({
      search,
      role,
      page,
      limit
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

async function createUser(request: NextRequest) {
  try {
    const body = await request.json();
    const validationResult = createUserSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { email, firstName, lastName, role, phone } = validationResult.data;

    // Check if user already exists
    const existingUser = await db.getUsers({ search: email });
    if (existingUser.data && existingUser.data.length > 0) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      );
    }

    // Create user with a temporary ID (in a real app, you'd integrate with your auth provider)
    const userId = crypto.randomUUID();

    const result = await db.createUser({
      id: userId,
      email,
      role,
      firstName,
      lastName,
      phone,
    });

    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      message: 'User created successfully',
      data: result.data
    }, { status: 201 });
  } catch (error) {
    console.error('Create user error:', error);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}

export const GET = withAuth(getUsers, { requireAdmin: true });
export const POST = withAuth(createUser, { requireAdmin: true });