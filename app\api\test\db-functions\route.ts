import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    console.log('Testing database functions...');
    
    // Test get_sales_analytics function
    const { data: salesData, error: salesError } = await supabase.rpc('get_sales_analytics');
    console.log('Sales analytics result:', { salesData, salesError });
    
    // Test get_low_stock_products function
    const { data: lowStockData, error: lowStockError } = await supabase.rpc('get_low_stock_products', { threshold: 10 });
    console.log('Low stock result:', { lowStockData, lowStockError });
    
    // Test basic orders query
    const { data: ordersData, error: ordersError } = await supabase
      .from('orders')
      .select(`
        *,
        items:order_items(*),
        user:users(*)
      `)
      .limit(10);
    console.log('Orders result:', { ordersData, ordersError });

    return NextResponse.json({
      success: true,
      message: 'Database functions test',
      data: {
        salesAnalytics: {
          data: salesData,
          error: salesError?.message || null
        },
        lowStockProducts: {
          data: lowStockData,
          error: lowStockError?.message || null
        },
        orders: {
          count: ordersData?.length || 0,
          error: ordersError?.message || null
        }
      }
    });
  } catch (error) {
    console.error('Database functions test error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Database functions test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}