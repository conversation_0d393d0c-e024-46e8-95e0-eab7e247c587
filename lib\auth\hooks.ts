// Authentication hooks for JOOKA E-commerce Platform
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

// Hook to get current user session
export function useAuth() {
  const { data: session, status } = useSession();
  
  return {
    user: session?.user || null,
    isLoading: status === 'loading',
    isAuthenticated: !!session?.user,
    isAdmin: session?.user?.role === 'admin',
    isCustomer: session?.user?.role === 'customer',
    isEmailVerified: session?.user?.emailVerified || false,
  };
}

// Hook to require authentication
export function useRequireAuth(redirectTo = '/auth/signin') {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !user) {
      router.push(redirectTo);
    }
  }, [user, isLoading, router, redirectTo]);

  return { user, isLoading };
}

// Hook to require admin access
export function useRequireAdmin(redirectTo = '/') {
  const { user, isLoading, isAdmin } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (!user) {
        router.push('/auth/signin');
      } else if (!isAdmin) {
        router.push(redirectTo);
      }
    }
  }, [user, isLoading, isAdmin, router, redirectTo]);

  return { user, isLoading, isAdmin };
}

// Hook to require email verification
export function useRequireEmailVerification(redirectTo = '/auth/verify-email') {
  const { user, isLoading, isEmailVerified } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && user && !isEmailVerified) {
      router.push(redirectTo);
    }
  }, [user, isLoading, isEmailVerified, router, redirectTo]);

  return { user, isLoading, isEmailVerified };
}

// Hook to redirect authenticated users
export function useRedirectIfAuthenticated(redirectTo = '/dashboard') {
  const { user, isLoading, isAdmin } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && user) {
      if (isAdmin) {
        router.push('/admin/dashboard');
      } else {
        router.push(redirectTo);
      }
    }
  }, [user, isLoading, isAdmin, router, redirectTo]);

  return { user, isLoading };
}