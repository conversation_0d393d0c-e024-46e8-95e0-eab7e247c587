'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { signOut, useSession } from 'next-auth/react';
import {
  LayoutDashboard,
  ShoppingBag,
  User,
  MapPin,
  CreditCard,
  Settings,
  LogOut,
  Bell,
} from 'lucide-react';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: 'Orders', href: '/dashboard/orders', icon: ShoppingBag },
  { name: 'Profile', href: '/dashboard/profile', icon: User },
  { name: 'Addresses', href: '/dashboard/addresses', icon: MapPin },
  { name: 'Payment Methods', href: '/dashboard/payments', icon: CreditCard },
  { name: 'Notifications', href: '/dashboard/notifications', icon: Bell },
  { name: 'Settings', href: '/dashboard/settings', icon: Settings },
];

export default function CustomerSidebar() {
  const pathname = usePathname();
  const { data: session } = useSession();

  return (
    <div className="fixed inset-y-0 left-0 z-50 w-64 bg-charcoal border-r border-gold/20">
      <div className="flex flex-col h-full">
        {/* Logo */}
        <div className="flex items-center justify-center h-16 px-4 border-b border-gold/20">
          <Link href="/" className="text-xl font-bold text-gold">
            JOOKA
          </Link>
        </div>

        {/* User Info */}
        <div className="px-4 py-6 border-b border-gold/20">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gold/20 rounded-full flex items-center justify-center">
              <User className="w-5 h-5 text-gold" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-white">
                {session?.user?.name || 'User'}
              </p>
              <p className="text-xs text-gray-400">
                {session?.user?.email}
              </p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2">
          {navigation.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`flex items-center px-4 py-3 text-sm font-medium rounded-md transition-colors ${
                  isActive
                    ? 'bg-gold text-black'
                    : 'text-gray-300 hover:bg-gold/10 hover:text-gold'
                }`}
              >
                <item.icon className="w-5 h-5 mr-3" />
                {item.name}
              </Link>
            );
          })}
        </nav>

        {/* Logout */}
        <div className="p-4 border-t border-gold/20">
          <button
            onClick={() => signOut({ callbackUrl: '/' })}
            className="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-300 rounded-md hover:bg-red-600/10 hover:text-red-400 transition-colors"
          >
            <LogOut className="w-5 h-5 mr-3" />
            Sign Out
          </button>
        </div>
      </div>
    </div>
  );
}