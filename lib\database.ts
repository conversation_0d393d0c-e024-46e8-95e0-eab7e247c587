import { createClient } from '@/lib/supabase/server'
import { ApiResponse, ErrorCodes } from '@/types/api'

// Generic database operation wrapper
export async function withDatabase<T>(
  operation: (supabase: ReturnType<typeof createClient>) => Promise<T>
): Promise<ApiResponse<T>> {
  try {
    const supabase = createClient()
    const data = await operation(supabase)
    
    return {
      success: true,
      data,
    }
  } catch (error) {
    console.error('Database operation failed:', error)
    
    return {
      success: false,
      error: {
        code: ErrorCodes.INTERNAL_ERROR,
        message: 'Database operation failed',
        details: error,
        timestamp: new Date(),
      },
    }
  }
}

// Helper function to handle Supabase responses
export function handleSupabaseResponse<T>(response: { data: T | null; error: any }) {
  if (response.error) {
    throw new Error(response.error.message)
  }
  
  return response.data
}