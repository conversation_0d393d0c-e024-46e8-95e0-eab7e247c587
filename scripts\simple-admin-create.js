#!/usr/bin/env node

/**
 * Simple Admin Creation Script - Straightforward approach
 */

const { createClient } = require('@supabase/supabase-js');

// Try to load dotenv
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// ===== EDIT THESE ADMIN DETAILS =====
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_FIRST_NAME = 'Admin';
const ADMIN_LAST_NAME = 'User';
const ADMIN_PASSWORD = 'AdminPassword123!';
// ====================================

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function createSimpleAdmin() {
  try {
    console.log('🔐 Simple Admin Creation for JOOKA E-commerce\n');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log(`👤 Name: ${ADMIN_FIRST_NAME} ${ADMIN_LAST_NAME}`);
    console.log(`🔐 Password: ${ADMIN_PASSWORD}`);
    console.log('');
    
    // Step 1: Clean up existing records
    console.log('🧹 Step 1: Cleaning up existing records...');
    
    // Get existing user ID if exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', ADMIN_EMAIL)
      .single();
    
    if (existingUser) {
      console.log('🔄 Found existing user, cleaning up...');
      
      // Delete profile
      await supabase
        .from('profiles')
        .delete()
        .eq('user_id', existingUser.id);
      
      // Delete user
      await supabase
        .from('users')
        .delete()
        .eq('id', existingUser.id);
      
      console.log('✅ Existing database records cleaned');
    }
    
    // Clean up auth user
    const { data: existingAuthUser } = await supabase.auth.admin.getUserByEmail(ADMIN_EMAIL);
    if (existingAuthUser.user) {
      await supabase.auth.admin.deleteUser(existingAuthUser.user.id);
      console.log('✅ Existing auth user cleaned');
    }
    
    // Step 2: Create new auth user
    console.log('\n🔄 Step 2: Creating auth user...');
    
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
      email_confirm: true,
      user_metadata: {
        first_name: ADMIN_FIRST_NAME,
        last_name: ADMIN_LAST_NAME,
      },
    });
    
    if (authError) {
      throw new Error(`Auth creation failed: ${authError.message}`);
    }
    
    if (!authData.user) {
      throw new Error('No user returned from auth creation');
    }
    
    console.log('✅ Auth user created successfully');
    console.log(`   User ID: ${authData.user.id}`);
    
    // Step 3: Create database user
    console.log('\n🔄 Step 3: Creating database user...');
    
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        id: authData.user.id,
        email: ADMIN_EMAIL,
        email_verified: true,
        role: 'admin',
      })
      .select()
      .single();
    
    if (userError) {
      console.error('User creation error details:', userError);
      throw new Error(`Database user creation failed: ${userError.message}`);
    }
    
    console.log('✅ Database user created successfully');
    
    // Step 4: Create profile
    console.log('\n🔄 Step 4: Creating user profile...');
    
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .insert({
        user_id: authData.user.id,
        first_name: ADMIN_FIRST_NAME,
        last_name: ADMIN_LAST_NAME,
      })
      .select()
      .single();
    
    if (profileError) {
      console.warn('⚠️  Profile creation warning:', profileError.message);
    } else {
      console.log('✅ Profile created successfully');
    }
    
    // Step 5: Verify creation
    console.log('\n🔍 Step 5: Verifying admin user...');
    
    const { data: verifyData, error: verifyError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        role,
        email_verified,
        profiles (
          first_name,
          last_name
        )
      `)
      .eq('email', ADMIN_EMAIL)
      .single();
    
    if (verifyError) {
      console.warn('⚠️  Verification warning:', verifyError.message);
    } else {
      console.log('✅ Verification successful:');
      console.log(`   ID: ${verifyData.id}`);
      console.log(`   Email: ${verifyData.email}`);
      console.log(`   Role: ${verifyData.role}`);
      console.log(`   Verified: ${verifyData.email_verified}`);
      if (verifyData.profiles?.[0]) {
        console.log(`   Name: ${verifyData.profiles[0].first_name} ${verifyData.profiles[0].last_name}`);
      }
    }
    
    console.log('\n🎉 Admin user created successfully!');
    console.log('\n📋 Login Details:');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log(`🔐 Password: ${ADMIN_PASSWORD}`);
    console.log('\n📋 Next Steps:');
    console.log('1. Go to http://localhost:3000/auth/signin');
    console.log('2. Sign in with the above credentials');
    console.log('3. You should be redirected to /admin/dashboard');
    console.log('4. Change your password in the admin settings');
    
  } catch (error) {
    console.error('\n❌ Admin creation failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Check your Supabase credentials in .env.local');
    console.error('2. Verify your Supabase project is active');
    console.error('3. Make sure the database schema is set up');
    console.error('4. Check Supabase dashboard for any issues');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  createSimpleAdmin();
}

module.exports = { createSimpleAdmin };