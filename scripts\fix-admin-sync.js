#!/usr/bin/env node

/**
 * Fix Admin Sync Script - Fixes sync issues between auth and database
 */

const { createClient } = require('@supabase/supabase-js');

// Try to load dotenv
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function fixAdminSync() {
  try {
    console.log('🔧 JOOKA E-commerce Admin Sync Fix\n');
    
    // Get all database users
    const { data: dbUsers, error: dbError } = await supabase
      .from('users')
      .select(`
        id, 
        email, 
        role, 
        email_verified,
        profiles (
          first_name,
          last_name
        )
      `);
    
    if (dbError) {
      throw new Error(`Failed to fetch database users: ${dbError.message}`);
    }
    
    console.log(`📊 Found ${dbUsers.length} users in database`);
    
    // Get all auth users
    const { data: authData, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      throw new Error(`Failed to fetch auth users: ${authError.message}`);
    }
    
    console.log(`📊 Found ${authData.users.length} users in auth`);
    
    // Find database users without auth users
    const authUserIds = new Set(authData.users.map(u => u.id));
    const orphanedDbUsers = dbUsers.filter(u => !authUserIds.has(u.id));
    
    console.log(`\n🔍 Found ${orphanedDbUsers.length} database users without auth records`);
    
    for (const dbUser of orphanedDbUsers) {
      console.log(`\n🔄 Processing user: ${dbUser.email} (${dbUser.role})`);
      
      const profile = dbUser.profiles?.[0];
      const firstName = profile?.first_name || 'Admin';
      const lastName = profile?.last_name || 'User';
      
      // Create a temporary password (user should change it)
      const tempPassword = 'TempPassword123!';
      
      console.log(`   Creating auth user with temp password: ${tempPassword}`);
      
      // Create auth user with the same ID
      const { data: newAuthUser, error: createError } = await supabase.auth.admin.createUser({
        email: dbUser.email,
        password: tempPassword,
        email_confirm: true,
        user_metadata: {
          first_name: firstName,
          last_name: lastName,
        },
      });
      
      if (createError) {
        console.error(`   ❌ Failed to create auth user: ${createError.message}`);
        continue;
      }
      
      console.log(`   ✅ Auth user created successfully`);
      
      // Update the database user ID to match the new auth user ID
      if (newAuthUser.user && newAuthUser.user.id !== dbUser.id) {
        console.log(`   🔄 Updating database user ID from ${dbUser.id} to ${newAuthUser.user.id}`);
        
        // Update user record
        const { error: updateUserError } = await supabase
          .from('users')
          .update({ id: newAuthUser.user.id })
          .eq('id', dbUser.id);
        
        if (updateUserError) {
          console.error(`   ❌ Failed to update user ID: ${updateUserError.message}`);
          continue;
        }
        
        // Update profile record
        const { error: updateProfileError } = await supabase
          .from('profiles')
          .update({ user_id: newAuthUser.user.id })
          .eq('user_id', dbUser.id);
        
        if (updateProfileError) {
          console.warn(`   ⚠️  Warning updating profile: ${updateProfileError.message}`);
        }
        
        console.log(`   ✅ Database records updated successfully`);
      }
      
      console.log(`   🎉 User ${dbUser.email} is now synced!`);
      console.log(`   📧 Email: ${dbUser.email}`);
      console.log(`   🔑 Role: ${dbUser.role}`);
      console.log(`   🔐 Temp Password: ${tempPassword}`);
      console.log(`   ⚠️  User should change password after first login!`);
    }
    
    // Also check for auth users without database records
    const dbUserIds = new Set(dbUsers.map(u => u.id));
    const orphanedAuthUsers = authData.users.filter(u => !dbUserIds.has(u.id));
    
    if (orphanedAuthUsers.length > 0) {
      console.log(`\n🔍 Found ${orphanedAuthUsers.length} auth users without database records`);
      
      for (const authUser of orphanedAuthUsers) {
        console.log(`\n🔄 Processing auth user: ${authUser.email}`);
        
        const firstName = authUser.user_metadata?.first_name || 'User';
        const lastName = authUser.user_metadata?.last_name || 'Name';
        
        // Create database user record
        const { error: userError } = await supabase
          .from('users')
          .insert({
            id: authUser.id,
            email: authUser.email,
            email_verified: !!authUser.email_confirmed_at,
            role: 'customer', // Default to customer, can be promoted later
          });
        
        if (userError) {
          console.error(`   ❌ Failed to create database user: ${userError.message}`);
          continue;
        }
        
        // Create profile record
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            user_id: authUser.id,
            first_name: firstName,
            last_name: lastName,
          });
        
        if (profileError) {
          console.warn(`   ⚠️  Warning creating profile: ${profileError.message}`);
        }
        
        console.log(`   ✅ Database records created for ${authUser.email}`);
      }
    }
    
    console.log('\n🎉 Admin sync fix completed!');
    console.log('\n📋 Next steps:');
    console.log('1. Try signing in with the email and temporary password');
    console.log('2. Change the password immediately after login');
    console.log('3. Verify admin access to /admin/dashboard');
    
  } catch (error) {
    console.error('\n❌ Admin sync fix failed:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  fixAdminSync();
}

module.exports = { fixAdminSync };