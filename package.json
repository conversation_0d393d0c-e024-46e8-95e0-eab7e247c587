{"name": "jooka-ecommerce", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:setup": "node scripts/setup-database.js", "db:reset": "node scripts/setup-database.js", "admin:create": "node scripts/create-admin.js", "admin:create-simple": "node scripts/create-admin-simple.js", "admin:fix": "node scripts/fix-admin-user.js", "admin:direct-fix": "node scripts/direct-admin-fix.js", "admin:bootstrap": "node scripts/bootstrap-admin.js", "admin:fresh": "node scripts/create-fresh-admin.js", "admin:promote": "node scripts/promote-to-admin.js", "admin:trigger-aware": "node scripts/trigger-aware-admin.js", "admin:cleanup": "node scripts/thorough-cleanup.js", "db:check": "node scripts/check-database.js"}, "dependencies": {"@headlessui/react": "^1.7.15", "@sanity/client": "^5.2.3", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.53.0", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "dotenv": "^16.6.1", "framer-motion": "^10.0.0", "lenis": "^1.3.8", "lucide-react": "^0.263.0", "next": "^14.0.0", "next-auth": "^4.24.11", "next-cloudinary": "^6.16.0", "react": "^18.2.0", "react-dom": "^18.2.0", "stripe": "^12.0.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.1", "zod": "^3.22.4", "zustand": "^4.3.5"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.14", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.24", "typescript": "^5.2.2"}}