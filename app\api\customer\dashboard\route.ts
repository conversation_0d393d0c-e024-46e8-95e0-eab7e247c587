// Customer dashboard API route
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';
import { db } from '@/lib/database/index';

async function getCustomerDashboard(request: NextRequest) {
  const user = (request as any).user;
  
  try {
    console.log('Fetching customer dashboard data for user:', user?.id);
    
    // Get user profile
    const userResult = await db.getUser(user.id);
    console.log('User result:', userResult);
    
    // Get recent orders
    const ordersResult = await db.getOrders({
      userId: user.id,
      page: 1,
      limit: 5,
    });
    console.log('Orders result:', ordersResult);

    // Get cart items
    const cartResult = await db.getCartItems(user.id);
    console.log('Cart result:', cartResult);

    // Get addresses
    const addressesResult = await db.getUserAddresses(user.id);
    console.log('Addresses result:', addressesResult);

    // Get payment methods
    const paymentMethodsResult = await db.getUserPaymentMethods(user.id);
    console.log('Payment methods result:', paymentMethodsResult);

    // Get notifications
    const notificationsResult = await db.getUserNotifications(user.id, true); // unread only
    console.log('Notifications result:', notificationsResult);

    const dashboardData = {
      user: userResult.data || { id: user.id, email: '<EMAIL>' },
      recentOrders: ordersResult.data || [],
      cart: {
        items: cartResult.data || [],
        itemCount: (cartResult.data || []).reduce((total, item) => total + item.quantity, 0),
      },
      addresses: addressesResult.data || [],
      paymentMethods: paymentMethodsResult.data || [],
      notifications: notificationsResult.data || [],
      stats: {
        totalOrders: ordersResult.pagination?.total || 0,
        cartItems: (cartResult.data || []).reduce((total, item) => total + item.quantity, 0),
        savedAddresses: (addressesResult.data || []).length,
        unreadNotifications: (notificationsResult.data || []).length,
      },
    };

    return NextResponse.json({ data: dashboardData });
  } catch (error) {
    console.error('Get customer dashboard error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export const GET = withAuth(getCustomerDashboard);