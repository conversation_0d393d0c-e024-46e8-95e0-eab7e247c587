// User registration API route
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { validateEmail, validatePassword, validateName } from '@/lib/validation';
import { z } from 'zod';

const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Registration request body:', { ...body, password: '[REDACTED]', confirmPassword: '[REDACTED]' });

    // Validate input
    const validationResult = registerSchema.safeParse(body);
    if (!validationResult.success) {
      console.log('Schema validation failed:', validationResult.error.errors);
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { email, password, firstName, lastName } = validationResult.data;

    // Additional validation
    if (!validateEmail(email)) {
      console.log('Email validation failed for:', email);
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    if (!validatePassword(password)) {
      console.log('Password validation failed - password does not meet requirements');
      return NextResponse.json(
        { error: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character' },
        { status: 400 }
      );
    }

    if (!validateName(firstName) || !validateName(lastName)) {
      console.log('Name validation failed for:', firstName, lastName);
      return NextResponse.json(
        { error: 'Names must contain only letters and spaces' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }

    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Create user with Supabase Auth
    console.log('Attempting to create user with Supabase Auth...');
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          first_name: firstName,
          last_name: lastName,
        },
        emailRedirectTo: `${process.env.NEXTAUTH_URL}/auth/callback`,
      },
    });

    console.log('Supabase auth response:', {
      hasData: !!authData,
      hasUser: !!authData?.user,
      hasError: !!authError,
      errorCode: authError?.message
    });

    if (authError) {
      console.error('Supabase auth error:', authError);

      // Provide more helpful error messages
      let errorMessage = authError.message;
      if (authError.message.includes('email_address_invalid')) {
        errorMessage = 'Please use a valid email address. Test domains like example.com are not allowed. Try using a real email domain like gmail.com, outlook.com, etc.';
      }

      return NextResponse.json(
        { error: errorMessage },
        { status: 400 }
      );
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'Failed to create user account' },
        { status: 500 }
      );
    }

    // The user and profile records will be created automatically by the database trigger
    // when the auth.users record is created

    return NextResponse.json({
      message: 'Registration successful. Please check your email to verify your account.',
      user: {
        id: authData.user.id,
        email: authData.user.email,
        emailVerified: false,
      },
    });

  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}