// Database setup script for JOOKA E-commerce Platform
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupDatabase() {
  try {
    console.log('Setting up JOOKA E-commerce database...');
    
    // Read schema file
    const schemaPath = path.join(__dirname, '..', 'lib', 'supabase', 'schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Read functions file
    const functionsPath = path.join(__dirname, '..', 'lib', 'supabase', 'functions.sql');
    const functions = fs.readFileSync(functionsPath, 'utf8');
    
    console.log('Executing schema...');
    // Note: Supabase client doesn't support executing raw SQL directly
    // This would need to be run in the Supabase SQL editor or via the CLI
    console.log('Please run the following SQL in your Supabase SQL editor:');
    console.log('\n--- SCHEMA ---');
    console.log(schema);
    console.log('\n--- FUNCTIONS ---');
    console.log(functions);
    
    // Test basic connection
    const { data, error } = await supabase
      .from('categories')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('Database tables not yet created. Please run the schema SQL in Supabase.');
      console.log('Error:', error.message);
    } else {
      console.log('Database connection successful!');
    }
    
  } catch (error) {
    console.error('Database setup error:', error);
  }
}

setupDatabase();