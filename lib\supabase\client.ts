import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey || supabaseUrl.includes('your_supabase')) {
    // Return a mock client during build time or when env vars are not set
    return null as any
  }

  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}