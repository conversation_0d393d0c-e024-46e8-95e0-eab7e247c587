// User addresses management API routes
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/middleware';
import { db } from '@/lib/database';
import { z } from 'zod';

const createAddressSchema = z.object({
  type: z.enum(['shipping', 'billing']),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  company: z.string().optional(),
  streetAddress1: z.string().min(1, 'Street address is required'),
  streetAddress2: z.string().optional(),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  postalCode: z.string().min(1, 'Postal code is required'),
  country: z.string().default('United States'),
  phone: z.string().optional(),
  isDefault: z.boolean().default(false),
});

async function getAddresses(request: NextRequest) {
  const user = (request as any).user;
  
  try {
    const result = await db.getUserAddresses(user.id);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: result.data });
  } catch (error) {
    console.error('Get addresses error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch addresses' },
      { status: 500 }
    );
  }
}

async function createAddress(request: NextRequest) {
  const user = (request as any).user;
  
  try {
    const body = await request.json();
    const validationResult = createAddressSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const addressData = {
      user_id: user.id,
      type: validationResult.data.type,
      first_name: validationResult.data.firstName,
      last_name: validationResult.data.lastName,
      company: validationResult.data.company,
      street_address_1: validationResult.data.streetAddress1,
      street_address_2: validationResult.data.streetAddress2,
      city: validationResult.data.city,
      state: validationResult.data.state,
      postal_code: validationResult.data.postalCode,
      country: validationResult.data.country,
      phone: validationResult.data.phone,
      is_default: validationResult.data.isDefault,
    };

    const result = await db.createAddress(addressData);
    
    if (result.error) {
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      message: 'Address created successfully',
      data: result.data 
    }, { status: 201 });
  } catch (error) {
    console.error('Create address error:', error);
    return NextResponse.json(
      { error: 'Failed to create address' },
      { status: 500 }
    );
  }
}

export const GET = withAuth(getAddresses);
export const POST = withAuth(createAddress);