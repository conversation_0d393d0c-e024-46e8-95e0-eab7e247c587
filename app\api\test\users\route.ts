// Test endpoint to verify users API without authentication
import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database/index';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing users API...');
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || undefined;
    const role = searchParams.get('role') || undefined;

    const result = await db.getUsers({
      search,
      role,
      page,
      limit
    });

    console.log('Users result:', result);

    return NextResponse.json({
      success: true,
      message: 'Users API test successful',
      result
    });
  } catch (error) {
    console.error('Test users API error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to test users API',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}