# Database Setup Guide

This guide explains how to set up the Supabase database for the JOOKA E-commerce Platform.

## Prerequisites

1. **Supabase Project**: Create a new project at [supabase.com](https://supabase.com)
2. **Environment Variables**: Configure your `.env.local` file with Supabase credentials

## Required Environment Variables

Add these variables to your `.env.local` file:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# Cloudinary Configuration (for file uploads)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
```

## Database Setup

### Automatic Setup (Recommended)

Run the automated setup script:

```bash
npm run db:setup
```

This script will:
- Create all database tables with proper relationships
- Set up Row Level Security (RLS) policies
- Create database functions for common operations
- Insert sample data for testing

### Manual Setup

If you prefer to set up the database manually:

1. **Schema**: Execute `lib/supabase/schema.sql` in your Supabase SQL editor
2. **Functions**: Execute `lib/supabase/functions.sql`
3. **RLS Policies**: Execute `lib/supabase/rls-policies.sql`

## Database Schema Overview

### Core Tables

- **users**: User accounts (extends Supabase auth.users)
- **profiles**: User profile information
- **categories**: Product categories with hierarchical support
- **products**: Product catalog with inventory tracking
- **orders**: Customer orders with status tracking
- **order_items**: Individual items within orders
- **cart_items**: Shopping cart persistence
- **addresses**: User shipping/billing addresses
- **payment_methods**: Tokenized payment method storage
- **notifications**: User notifications system
- **inventory_transactions**: Inventory change audit trail
- **audit_logs**: System-wide audit logging

### Key Features

- **Row Level Security (RLS)**: All tables have proper security policies
- **Automatic Triggers**: Updated timestamps and user creation handling
- **Database Functions**: Common operations like order creation and inventory management
- **Real-time Support**: Tables configured for Supabase real-time subscriptions

## Security Model

### User Roles

- **admin**: Full access to all data and operations
- **customer**: Access to own data only (orders, profile, cart, etc.)

### RLS Policies

- Users can only access their own data (orders, cart, profile, etc.)
- Admins have full access to all data
- Public read access for active products and categories
- Secure audit trail that prevents tampering

## Database Functions

### Order Management

- `create_order_with_items()`: Creates orders with inventory updates
- `update_order_status()`: Updates order status with notifications
- `calculate_order_totals()`: Calculates order totals with tax

### Inventory Management

- `update_product_inventory()`: Updates inventory with audit trail
- `get_low_stock_products()`: Returns products below threshold

### Analytics

- `get_sales_analytics()`: Returns sales data and metrics

### Utilities

- `generate_order_number()`: Generates unique order numbers

## Testing the Setup

After running the setup script, verify everything works:

1. **Check Tables**: Visit your Supabase dashboard and verify all tables exist
2. **Test Authentication**: Try creating a user account
3. **Test RLS**: Verify users can only access their own data
4. **Test Functions**: Try calling database functions from the API

## Sample Data

The setup script includes sample data:

- 5 product categories (Electronics, Clothing, Home & Garden, Books, Sports)
- 2 sample products with images and proper categorization
- Proper inventory levels for testing

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure you're using the service role key, not the anon key
2. **Connection Issues**: Verify your Supabase URL and keys are correct
3. **RLS Errors**: Make sure RLS policies are applied after schema creation

### Reset Database

To completely reset the database:

```bash
npm run db:reset
```

**Warning**: This will delete all existing data!

### Manual Verification

You can manually verify the setup by running these queries in Supabase SQL editor:

```sql
-- Check if all tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Check if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';

-- Check sample data
SELECT COUNT(*) as category_count FROM categories;
SELECT COUNT(*) as product_count FROM products;
```

## Next Steps

After successful database setup:

1. Start the development server: `npm run dev`
2. Test user registration and authentication
3. Test product browsing and cart functionality
4. Configure Stripe for payment processing
5. Set up Cloudinary for image uploads

## Support

If you encounter issues:

1. Check the Supabase dashboard for error logs
2. Verify all environment variables are set correctly
3. Ensure your Supabase project has the required permissions
4. Check the console output for specific error messages