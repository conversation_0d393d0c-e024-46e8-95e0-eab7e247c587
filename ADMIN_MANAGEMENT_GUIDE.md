# JOOKA E-commerce Admin Management Guide

This guide provides comprehensive instructions for managing admin accounts in the JOOKA E-commerce platform.

## 🎉 Quick Start - Your Admin is Ready!

**Your admin account has been successfully created:**

- **Email:** `<EMAIL>`
- **Password:** `AdminPassword123!`
- **Role:** `admin`

### Test Your Admin Access

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Go to: http://localhost:3000/auth/signin

3. Sign in with the credentials above

4. You should be redirected to: `/admin/dashboard`

5. Verify you can access all admin features

## 🛠️ Available Admin Scripts

The platform includes several scripts for admin management:

### Core Scripts

```bash
# Check database status and existing users
npm run db:check

# Promote existing user to admin (recommended)
npm run admin:promote

# Create admin with database triggers (recommended)
npm run admin:trigger-aware

# Clean up all users and start fresh
npm run admin:cleanup
```

### Legacy Scripts (for troubleshooting)

```bash
# Original admin creation script
npm run admin:create

# Simple admin creation
npm run admin:create-simple

# Various fix scripts
npm run admin:fix
npm run admin:direct-fix
npm run admin:bootstrap
npm run admin:fresh
```

## 🔧 How Admin Creation Works

### Database Triggers

The platform uses database triggers that automatically create user records when auth users are created:

1. **Auth User Created** → Triggers `handle_new_user()` function
2. **Function Creates:**
   - User record in `public.users` table (role: `customer`)
   - Profile record in `public.profiles` table
3. **Manual Promotion** → Update role from `customer` to `admin`

### User Flow

```
Supabase Auth User → Database Trigger → User Record → Role Promotion → Admin Access
```

## 📋 Admin Account Structure

### Auth User (Supabase Auth)
- Email and password authentication
- Email confirmation status
- User metadata (first_name, last_name)

### Database User (public.users)
- Links to auth user by ID
- Role: `admin` or `customer`
- Email verification status
- Timestamps

### Profile (public.profiles)
- User details (first_name, last_name)
- Avatar URL
- Additional profile information

## 🔍 Troubleshooting

### Common Issues

#### 1. "User already exists" Error
```bash
# Check current state
npm run db:check

# Promote existing user instead of creating new
npm run admin:promote
```

#### 2. "Duplicate key" Error
```bash
# This happens when database triggers create records automatically
# Use the trigger-aware script instead
npm run admin:trigger-aware
```

#### 3. "Auth user exists but no database record"
```bash
# Clean up and recreate
npm run admin:cleanup
npm run admin:trigger-aware
```

#### 4. "Database user exists but no auth user"
```bash
# Clean up and recreate
npm run admin:cleanup
npm run admin:trigger-aware
```

### Manual Verification

Check your Supabase dashboard:

1. **Authentication → Users**: Verify auth user exists
2. **Table Editor → users**: Verify database user with `admin` role
3. **Table Editor → profiles**: Verify profile record exists

## 🔐 Security Best Practices

### Password Management
- Change default password after first login
- Use strong passwords (8+ chars, mixed case, numbers, symbols)
- Consider using a password manager

### Environment Variables
Ensure these are set in `.env.local`:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_key
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_secret
```

### Production Considerations
- Use environment-specific admin accounts
- Implement 2FA for admin accounts
- Regular password rotation
- Monitor admin access logs

## 🚀 Admin Features

Once logged in as admin, you have access to:

### Admin Dashboard (`/admin/dashboard`)
- System overview
- Quick actions
- Recent activity

### User Management (`/admin/users`)
- View all users
- Promote users to admin
- User activity monitoring

### Product Management (`/admin/products`)
- Add/edit/delete products
- Inventory management
- Product categories

### Order Management (`/admin/orders`)
- View all orders
- Update order status
- Order fulfillment

### Inventory Management (`/admin/inventory`)
- Stock levels
- Low stock alerts
- Inventory adjustments

## 📊 Database Schema

### Users Table
```sql
CREATE TABLE public.users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    role user_role DEFAULT 'customer',
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Profiles Table
```sql
CREATE TABLE public.profiles (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES public.users(id),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🔄 Maintenance Scripts

### Regular Maintenance
```bash
# Check system health
npm run db:check

# Verify admin access
npm run admin:promote
```

### Emergency Recovery
```bash
# Complete reset (use with caution)
npm run admin:cleanup
npm run admin:trigger-aware
```

## 📞 Support

If you continue to have issues:

1. Check the console logs for detailed error messages
2. Verify your Supabase project is active and accessible
3. Ensure all environment variables are correctly set
4. Check Supabase dashboard for any service issues
5. Review the database schema and RLS policies

## 🎯 Next Steps

After setting up your admin account:

1. **Change Password**: Update the default password
2. **Configure Settings**: Set up your admin preferences
3. **Add Products**: Start adding your product catalog
4. **Test Features**: Verify all admin functionality works
5. **Set Up Production**: Configure production admin accounts

---

**Your admin account is ready to use! 🎉**

Email: `<EMAIL>`  
Password: `AdminPassword123!`  
Dashboard: http://localhost:3000/admin/dashboard