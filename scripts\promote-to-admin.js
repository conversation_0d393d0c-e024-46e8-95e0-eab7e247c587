#!/usr/bin/env node

/**
 * Promote Existing User to Admin <PERSON>
 */

const { createClient } = require('@supabase/supabase-js');

// Try to load dotenv
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// ===== EDIT THIS EMAIL =====
const USER_EMAIL = '<EMAIL>';
// ===========================

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function promoteToAdmin() {
  try {
    console.log('👑 Promote User to Admin for JOOKA E-commerce\n');
    console.log(`📧 Target Email: ${USER_EMAIL}`);
    console.log('');
    
    // Step 1: Find the user
    console.log('🔍 Step 1: Finding user...');
    
    const { data: userData, error: findError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        role,
        email_verified,
        profiles (
          first_name,
          last_name
        )
      `)
      .eq('email', USER_EMAIL)
      .single();
    
    if (findError) {
      throw new Error(`User not found: ${findError.message}`);
    }
    
    console.log('✅ User found:');
    console.log(`   ID: ${userData.id}`);
    console.log(`   Email: ${userData.email}`);
    console.log(`   Current Role: ${userData.role}`);
    console.log(`   Email Verified: ${userData.email_verified}`);
    
    if (userData.profiles && userData.profiles.length > 0) {
      const profile = userData.profiles[0];
      console.log(`   Name: ${profile.first_name} ${profile.last_name}`);
    }
    
    // Step 2: Check if already admin
    if (userData.role === 'admin') {
      console.log('\n✅ User is already an admin!');
      console.log('\n📋 Login Details:');
      console.log(`📧 Email: ${USER_EMAIL}`);
      console.log(`🔐 Password: AdminPassword123! (if you set it)`);
      console.log('\n📋 Next Steps:');
      console.log('1. Go to: http://localhost:3000/auth/signin');
      console.log('2. Sign in with your credentials');
      console.log('3. You should be redirected to: /admin/dashboard');
      return;
    }
    
    // Step 3: Promote to admin
    console.log('\n👑 Step 2: Promoting user to admin...');
    
    const { data: updateData, error: updateError } = await supabase
      .from('users')
      .update({ role: 'admin' })
      .eq('id', userData.id)
      .select()
      .single();
    
    if (updateError) {
      throw new Error(`Failed to promote user: ${updateError.message}`);
    }
    
    console.log('✅ User promoted to admin successfully!');
    console.log(`   New Role: ${updateData.role}`);
    
    // Step 4: Final verification
    console.log('\n🔍 Step 3: Final verification...');
    
    const { data: verifyData, error: verifyError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        role,
        email_verified,
        profiles (
          first_name,
          last_name
        )
      `)
      .eq('id', userData.id)
      .single();
    
    if (verifyError) {
      console.warn('⚠️  Verification warning:', verifyError.message);
    } else {
      console.log('✅ Final verification successful:');
      console.log(`   ID: ${verifyData.id}`);
      console.log(`   Email: ${verifyData.email}`);
      console.log(`   Role: ${verifyData.role}`);
      console.log(`   Email Verified: ${verifyData.email_verified}`);
      
      if (verifyData.profiles && verifyData.profiles.length > 0) {
        const profile = verifyData.profiles[0];
        console.log(`   Name: ${profile.first_name} ${profile.last_name}`);
      }
    }
    
    console.log('\n🎉 User successfully promoted to admin!');
    console.log('\n📋 Login Details:');
    console.log(`📧 Email: ${USER_EMAIL}`);
    console.log(`🔐 Password: AdminPassword123! (if you set it, otherwise use your existing password)`);
    console.log('\n📋 Test Your Admin Access:');
    console.log('1. Start your dev server: npm run dev');
    console.log('2. Go to: http://localhost:3000/auth/signin');
    console.log('3. Sign in with your credentials');
    console.log('4. You should be redirected to: /admin/dashboard');
    console.log('5. Verify you can access all admin features');
    console.log('\n✨ Your admin account is now ready!');
    
  } catch (error) {
    console.error('\n❌ Promotion failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Make sure the user exists in the database');
    console.error('2. Check your Supabase credentials');
    console.error('3. Verify RLS policies allow role updates');
    console.error('4. Try running: node scripts/check-database.js');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  promoteToAdmin();
}

module.exports = { promoteToAdmin };