#!/usr/bin/env node

/**
 * Trigger-Aware Admin Creation Script
 * Works with the database triggers that auto-create user records
 */

const { createClient } = require('@supabase/supabase-js');

// Try to load dotenv
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// ===== EDIT THESE ADMIN DETAILS =====
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_FIRST_NAME = 'Admin';
const ADMIN_LAST_NAME = 'User';
const ADMIN_PASSWORD = 'AdminPassword123!';
// ====================================

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function createTriggerAwareAdmin() {
  try {
    console.log('🔐 Trigger-Aware Admin Creation for JOOKA E-commerce\n');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log(`👤 Name: ${ADMIN_FIRST_NAME} ${ADMIN_LAST_NAME}`);
    console.log(`🔐 Password: ${ADMIN_PASSWORD}`);
    console.log('');
    console.log('ℹ️  This script works with database triggers that auto-create user records');
    console.log('');
    
    // Step 1: Create auth user (this will trigger automatic database record creation)
    console.log('🔄 Step 1: Creating auth user (triggers will handle database records)...');
    
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
      email_confirm: true,
      user_metadata: {
        first_name: ADMIN_FIRST_NAME,
        last_name: ADMIN_LAST_NAME,
      },
    });
    
    if (authError) {
      throw new Error(`Auth creation failed: ${authError.message}`);
    }
    
    if (!authData.user) {
      throw new Error('No user returned from auth creation');
    }
    
    console.log('✅ Auth user created successfully');
    console.log(`   User ID: ${authData.user.id}`);
    console.log('   Database triggers should have created user and profile records');
    
    // Step 2: Wait for triggers to complete
    console.log('\n⏳ Step 2: Waiting for database triggers to complete...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Step 3: Update the user role to admin (triggers create as customer by default)
    console.log('\n🔄 Step 3: Promoting user to admin role...');
    
    const { data: updateData, error: updateError } = await supabase
      .from('users')
      .update({ role: 'admin' })
      .eq('id', authData.user.id)
      .select()
      .single();
    
    if (updateError) {
      throw new Error(`Failed to promote user to admin: ${updateError.message}`);
    }
    
    console.log('✅ User promoted to admin successfully');
    console.log(`   Role: ${updateData.role}`);
    
    // Step 4: Verify the complete setup
    console.log('\n🔍 Step 4: Verifying complete admin setup...');
    
    const { data: verifyData, error: verifyError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        role,
        email_verified,
        created_at,
        profiles (
          id,
          first_name,
          last_name
        )
      `)
      .eq('id', authData.user.id)
      .single();
    
    if (verifyError) {
      console.warn('⚠️  Verification warning:', verifyError.message);
    } else {
      console.log('✅ Complete verification successful:');
      console.log(`   Database ID: ${verifyData.id}`);
      console.log(`   Email: ${verifyData.email}`);
      console.log(`   Role: ${verifyData.role}`);
      console.log(`   Email Verified: ${verifyData.email_verified}`);
      console.log(`   Created: ${verifyData.created_at}`);
      
      if (verifyData.profiles && verifyData.profiles.length > 0) {
        const profile = verifyData.profiles[0];
        console.log(`   Profile ID: ${profile.id}`);
        console.log(`   Name: ${profile.first_name} ${profile.last_name}`);
      }
    }
    
    // Step 5: Test auth user exists
    console.log('\n🔍 Step 5: Verifying auth user...');
    
    const { data: authUsers, error: listError } = await supabase.auth.admin.listUsers();
    
    if (listError) {
      console.warn('⚠️  Could not list auth users:', listError.message);
    } else {
      const adminAuthUser = authUsers.users.find(u => u.id === authData.user.id);
      if (adminAuthUser) {
        console.log('✅ Auth user verified:');
        console.log(`   Auth ID: ${adminAuthUser.id}`);
        console.log(`   Email: ${adminAuthUser.email}`);
        console.log(`   Email Confirmed: ${!!adminAuthUser.email_confirmed_at}`);
        console.log(`   Metadata: ${JSON.stringify(adminAuthUser.user_metadata)}`);
      } else {
        console.warn('⚠️  Auth user not found in list');
      }
    }
    
    console.log('\n🎉 Admin user created successfully with database triggers!');
    console.log('\n📋 Login Credentials:');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log(`🔐 Password: ${ADMIN_PASSWORD}`);
    console.log('\n📋 Test Your Admin Account:');
    console.log('1. Start your dev server: npm run dev');
    console.log('2. Go to: http://localhost:3000/auth/signin');
    console.log('3. Sign in with the credentials above');
    console.log('4. You should be redirected to: /admin/dashboard');
    console.log('5. Verify you can access all admin features');
    console.log('\n✨ Your admin account is now working perfectly!');
    console.log('\n🔧 How this worked:');
    console.log('- Created auth user with admin metadata');
    console.log('- Database triggers automatically created user and profile records');
    console.log('- Updated the user role from customer to admin');
    console.log('- All records are now properly synced');
    
  } catch (error) {
    console.error('\n❌ Trigger-aware admin creation failed:', error.message);
    console.error('\n🔧 Troubleshooting:');
    console.error('1. Check your Supabase credentials in .env.local');
    console.error('2. Verify your database triggers are set up correctly');
    console.error('3. Make sure RLS policies allow admin operations');
    console.error('4. Check Supabase dashboard for any issues');
    console.error('5. Try running: node scripts/check-database.js');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  createTriggerAwareAdmin();
}

module.exports = { createTriggerAwareAdmin };