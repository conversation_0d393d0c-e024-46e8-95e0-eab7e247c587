-- Sample data for JOOKA E-commerce Platform
-- Run this after setting up the schema

-- Insert sample categories
INSERT INTO public.categories (id, name, slug, description, is_active, sort_order) VALUES
  (uuid_generate_v4(), 'Electronics', 'electronics', 'Electronic devices and gadgets', true, 1),
  (uuid_generate_v4(), 'Clothing', 'clothing', 'Fashion and apparel', true, 2),
  (uuid_generate_v4(), 'Home & Garden', 'home-garden', 'Home improvement and garden supplies', true, 3),
  (uuid_generate_v4(), 'Books', 'books', 'Books and educational materials', true, 4),
  (uuid_generate_v4(), 'Sports', 'sports', 'Sports and outdoor equipment', true, 5);

-- Insert sample products
WITH category_ids AS (
  SELECT id, slug FROM public.categories
)
INSERT INTO public.products (id, name, slug, description, short_description, price, category_id, inventory_count, status, featured) 
SELECT 
  uuid_generate_v4(),
  product_data.name,
  product_data.slug,
  product_data.description,
  product_data.short_description,
  product_data.price,
  c.id,
  product_data.inventory_count,
  'active'::product_status,
  product_data.featured
FROM (VALUES
  ('Wireless Headphones', 'wireless-headphones', 'High-quality wireless headphones with noise cancellation', 'Premium wireless headphones', 199.99, 'electronics', 50, true),
  ('Smartphone', 'smartphone', 'Latest model smartphone with advanced features', 'Advanced smartphone', 699.99, 'electronics', 25, true),
  ('Laptop', 'laptop', 'High-performance laptop for work and gaming', 'Performance laptop', 1299.99, 'electronics', 15, false),
  ('T-Shirt', 't-shirt', 'Comfortable cotton t-shirt in various colors', 'Cotton t-shirt', 29.99, 'clothing', 100, false),
  ('Jeans', 'jeans', 'Classic denim jeans with perfect fit', 'Denim jeans', 79.99, 'clothing', 75, true),
  ('Coffee Maker', 'coffee-maker', 'Automatic coffee maker with programmable settings', 'Programmable coffee maker', 149.99, 'home-garden', 30, false),
  ('Programming Book', 'programming-book', 'Learn modern web development techniques', 'Web development guide', 49.99, 'books', 40, false),
  ('Running Shoes', 'running-shoes', 'Lightweight running shoes for all terrains', 'Athletic running shoes', 129.99, 'sports', 60, true)
) AS product_data(name, slug, description, short_description, price, category_slug, inventory_count, featured)
JOIN category_ids c ON c.slug = product_data.category_slug;

-- Insert a sample admin user (you'll need to update the ID to match your actual user ID)
-- This is just a placeholder - you'll need to create the user through the auth system first
INSERT INTO public.users (id, email, role, email_verified) VALUES
  ('00000000-0000-0000-0000-000000000001', '<EMAIL>', 'admin', true)
ON CONFLICT (email) DO UPDATE SET role = 'admin';

-- Insert sample profile for admin
INSERT INTO public.profiles (user_id, first_name, last_name) VALUES
  ('00000000-0000-0000-0000-000000000001', 'Admin', 'User')
ON CONFLICT (user_id) DO UPDATE SET first_name = 'Admin', last_name = 'User';

-- Insert sample customer user
INSERT INTO public.users (id, email, role, email_verified) VALUES
  ('00000000-0000-0000-0000-000000000002', '<EMAIL>', 'customer', true)
ON CONFLICT (email) DO UPDATE SET role = 'customer';

-- Insert sample profile for customer
INSERT INTO public.profiles (user_id, first_name, last_name) VALUES
  ('00000000-0000-0000-0000-000000000002', 'John', 'Doe')
ON CONFLICT (user_id) DO UPDATE SET first_name = 'John', last_name = 'Doe';