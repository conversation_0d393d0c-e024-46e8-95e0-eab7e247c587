-- Row Level Security (RLS) Policies for JOOKA E-commerce Platform
-- This file contains all RLS policies to secure data access

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventory_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Helper function to get current user role
CREATE OR REPLACE FUNCTION get_user_role(user_uuid UUID)
R<PERSON>URNS user_role AS $$
BEGIN
    RETURN (SELECT role FROM public.users WHERE id = user_uuid);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin(user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (SELECT role = 'admin' FROM public.users WHERE id = user_uuid);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- USERS TABLE POLICIES
-- Admins can see all users, customers can only see themselves
CREATE POLICY "Users: Admins can view all users" ON public.users
    FOR SELECT USING (
        is_admin(auth.uid()) OR id = auth.uid()
    );

-- Only admins can insert users (registration handled by auth triggers)
CREATE POLICY "Users: Only admins can insert users" ON public.users
    FOR INSERT WITH CHECK (is_admin(auth.uid()));

-- Users can update their own record, admins can update any
CREATE POLICY "Users: Users can update own record" ON public.users
    FOR UPDATE USING (
        is_admin(auth.uid()) OR id = auth.uid()
    );

-- Only admins can delete users
CREATE POLICY "Users: Only admins can delete users" ON public.users
    FOR DELETE USING (is_admin(auth.uid()));

-- PROFILES TABLE POLICIES
-- Users can view their own profile, admins can view all
CREATE POLICY "Profiles: Users can view own profile" ON public.profiles
    FOR SELECT USING (
        is_admin(auth.uid()) OR user_id = auth.uid()
    );

-- Users can insert their own profile
CREATE POLICY "Profiles: Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Users can update their own profile, admins can update any
CREATE POLICY "Profiles: Users can update own profile" ON public.profiles
    FOR UPDATE USING (
        is_admin(auth.uid()) OR user_id = auth.uid()
    );

-- Users can delete their own profile, admins can delete any
CREATE POLICY "Profiles: Users can delete own profile" ON public.profiles
    FOR DELETE USING (
        is_admin(auth.uid()) OR user_id = auth.uid()
    );

-- CATEGORIES TABLE POLICIES
-- Categories are public for reading
CREATE POLICY "Categories: Public read access" ON public.categories
    FOR SELECT USING (is_active = true OR is_admin(auth.uid()));

-- Only admins can modify categories
CREATE POLICY "Categories: Only admins can insert" ON public.categories
    FOR INSERT WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Categories: Only admins can update" ON public.categories
    FOR UPDATE USING (is_admin(auth.uid()));

CREATE POLICY "Categories: Only admins can delete" ON public.categories
    FOR DELETE USING (is_admin(auth.uid()));

-- PRODUCTS TABLE POLICIES
-- Products are public for reading (active products)
CREATE POLICY "Products: Public read access for active products" ON public.products
    FOR SELECT USING (
        (status = 'active' AND inventory_count > 0) OR is_admin(auth.uid())
    );

-- Only admins can modify products
CREATE POLICY "Products: Only admins can insert" ON public.products
    FOR INSERT WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Products: Only admins can update" ON public.products
    FOR UPDATE USING (is_admin(auth.uid()));

CREATE POLICY "Products: Only admins can delete" ON public.products
    FOR DELETE USING (is_admin(auth.uid()));

-- ADDRESSES TABLE POLICIES
-- Users can only access their own addresses
CREATE POLICY "Addresses: Users can view own addresses" ON public.addresses
    FOR SELECT USING (
        is_admin(auth.uid()) OR user_id = auth.uid()
    );

CREATE POLICY "Addresses: Users can insert own addresses" ON public.addresses
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Addresses: Users can update own addresses" ON public.addresses
    FOR UPDATE USING (
        is_admin(auth.uid()) OR user_id = auth.uid()
    );

CREATE POLICY "Addresses: Users can delete own addresses" ON public.addresses
    FOR DELETE USING (
        is_admin(auth.uid()) OR user_id = auth.uid()
    );

-- PAYMENT METHODS TABLE POLICIES
-- Users can only access their own payment methods
CREATE POLICY "Payment methods: Users can view own methods" ON public.payment_methods
    FOR SELECT USING (
        is_admin(auth.uid()) OR user_id = auth.uid()
    );

CREATE POLICY "Payment methods: Users can insert own methods" ON public.payment_methods
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Payment methods: Users can update own methods" ON public.payment_methods
    FOR UPDATE USING (
        is_admin(auth.uid()) OR user_id = auth.uid()
    );

CREATE POLICY "Payment methods: Users can delete own methods" ON public.payment_methods
    FOR DELETE USING (
        is_admin(auth.uid()) OR user_id = auth.uid()
    );

-- ORDERS TABLE POLICIES
-- Users can view their own orders, admins can view all
CREATE POLICY "Orders: Users can view own orders" ON public.orders
    FOR SELECT USING (
        is_admin(auth.uid()) OR user_id = auth.uid()
    );

-- Authenticated users can create orders
CREATE POLICY "Orders: Authenticated users can create orders" ON public.orders
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Only admins can update orders (status changes, etc.)
CREATE POLICY "Orders: Only admins can update orders" ON public.orders
    FOR UPDATE USING (is_admin(auth.uid()));

-- Only admins can delete orders
CREATE POLICY "Orders: Only admins can delete orders" ON public.orders
    FOR DELETE USING (is_admin(auth.uid()));

-- ORDER ITEMS TABLE POLICIES
-- Users can view items from their own orders, admins can view all
CREATE POLICY "Order items: Users can view own order items" ON public.order_items
    FOR SELECT USING (
        is_admin(auth.uid()) OR 
        order_id IN (SELECT id FROM public.orders WHERE user_id = auth.uid())
    );

-- Order items are created when orders are created
CREATE POLICY "Order items: Can insert with valid order" ON public.order_items
    FOR INSERT WITH CHECK (
        order_id IN (
            SELECT id FROM public.orders 
            WHERE user_id = auth.uid() OR is_admin(auth.uid())
        )
    );

-- Only admins can update order items
CREATE POLICY "Order items: Only admins can update" ON public.order_items
    FOR UPDATE USING (is_admin(auth.uid()));

-- Only admins can delete order items
CREATE POLICY "Order items: Only admins can delete" ON public.order_items
    FOR DELETE USING (is_admin(auth.uid()));

-- CART ITEMS TABLE POLICIES
-- Users can only access their own cart items
CREATE POLICY "Cart items: Users can view own cart" ON public.cart_items
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Cart items: Users can insert own cart items" ON public.cart_items
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Cart items: Users can update own cart items" ON public.cart_items
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Cart items: Users can delete own cart items" ON public.cart_items
    FOR DELETE USING (user_id = auth.uid());

-- INVENTORY TRANSACTIONS TABLE POLICIES
-- Only admins can view inventory transactions
CREATE POLICY "Inventory: Only admins can view transactions" ON public.inventory_transactions
    FOR SELECT USING (is_admin(auth.uid()));

-- Only admins can create inventory transactions
CREATE POLICY "Inventory: Only admins can create transactions" ON public.inventory_transactions
    FOR INSERT WITH CHECK (is_admin(auth.uid()));

-- No updates or deletes allowed on inventory transactions (audit trail)
CREATE POLICY "Inventory: No updates allowed" ON public.inventory_transactions
    FOR UPDATE USING (false);

CREATE POLICY "Inventory: No deletes allowed" ON public.inventory_transactions
    FOR DELETE USING (false);

-- NOTIFICATIONS TABLE POLICIES
-- Users can view their own notifications, admins can view all
CREATE POLICY "Notifications: Users can view own notifications" ON public.notifications
    FOR SELECT USING (
        is_admin(auth.uid()) OR user_id = auth.uid()
    );

-- System can create notifications for users
CREATE POLICY "Notifications: System can create notifications" ON public.notifications
    FOR INSERT WITH CHECK (true);

-- Users can update their own notifications (mark as read), admins can update any
CREATE POLICY "Notifications: Users can update own notifications" ON public.notifications
    FOR UPDATE USING (
        is_admin(auth.uid()) OR user_id = auth.uid()
    );

-- Users can delete their own notifications, admins can delete any
CREATE POLICY "Notifications: Users can delete own notifications" ON public.notifications
    FOR DELETE USING (
        is_admin(auth.uid()) OR user_id = auth.uid()
    );

-- AUDIT LOGS TABLE POLICIES
-- Only admins can view audit logs
CREATE POLICY "Audit logs: Only admins can view" ON public.audit_logs
    FOR SELECT USING (is_admin(auth.uid()));

-- System can create audit logs
CREATE POLICY "Audit logs: System can create logs" ON public.audit_logs
    FOR INSERT WITH CHECK (true);

-- No updates or deletes allowed on audit logs
CREATE POLICY "Audit logs: No updates allowed" ON public.audit_logs
    FOR UPDATE USING (false);

CREATE POLICY "Audit logs: No deletes allowed" ON public.audit_logs
    FOR DELETE USING (false);

-- Create function to automatically create user record when auth user is created
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, email_verified)
    VALUES (NEW.id, NEW.email, NEW.email_confirmed_at IS NOT NULL);
    
    INSERT INTO public.profiles (user_id, first_name, last_name)
    VALUES (NEW.id, 
            COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
            COALESCE(NEW.raw_user_meta_data->>'last_name', ''));
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to sync user email updates
CREATE OR REPLACE FUNCTION public.handle_user_update()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE public.users 
    SET 
        email = NEW.email,
        email_verified = NEW.email_confirmed_at IS NOT NULL,
        updated_at = NOW()
    WHERE id = NEW.id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for user updates
DROP TRIGGER IF EXISTS on_auth_user_updated ON auth.users;
CREATE TRIGGER on_auth_user_updated
    AFTER UPDATE ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_user_update();