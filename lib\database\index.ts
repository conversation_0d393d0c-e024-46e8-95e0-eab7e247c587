// Database service utilities for JOOKA E-commerce Platform
import { createClient } from '@/lib/supabase/client';
import type { 
  Database, 
  User, 
  UserProfile, 
  Product, 
  Category, 
  Order, 
  OrderItem,
  CartItem,
  Address,
  PaymentMethod,
  Notification,
  SalesAnalytics,
  LowStockProduct,
  ProductWithCategory,
  OrderWithItems,
  CartItemWithProduct,
  UserWithProfile,
  ApiResponse,
  PaginatedResponse
} from '@/types/database';

export class DatabaseService {
  private supabase = createClient();

  // User operations
  async getUser(userId: string): Promise<ApiResponse<UserWithProfile>> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select(`
          *,
          profile:profiles(*)
        `)
        .eq('id', userId)
        .single();

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async updateUserProfile(userId: string, profileData: Partial<UserProfile>): Promise<ApiResponse<UserProfile>> {
    try {
      const { data, error } = await this.supabase
        .from('profiles')
        .update(profileData)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async getUsers(params?: {
    search?: string;
    role?: string;
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<UserWithProfile>> {
    try {
      let query = this.supabase
        .from('users')
        .select(`
          *,
          profile:profiles(*)
        `, { count: 'exact' });

      // Apply filters
      if (params?.search) {
        query = query.or(`email.ilike.%${params.search}%,profile.first_name.ilike.%${params.search}%,profile.last_name.ilike.%${params.search}%`);
      }
      if (params?.role) {
        query = query.eq('role', params.role);
      }

      // Apply pagination
      const page = params?.page || 1;
      const limit = params?.limit || 20;
      const from = (page - 1) * limit;
      const to = from + limit - 1;

      query = query.range(from, to).order('created_at', { ascending: false });

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        data: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      };
    } catch (error) {
      throw new Error((error as Error).message);
    }
  }

  async deleteUser(userId: string): Promise<ApiResponse<void>> {
    try {
      // First delete the user's profile (foreign key constraint)
      const { error: profileError } = await this.supabase
        .from('profiles')
        .delete()
        .eq('user_id', userId);

      if (profileError) {
        console.warn('Warning deleting profile:', profileError.message);
      }

      // Delete related data
      await Promise.all([
        // Delete addresses
        this.supabase.from('addresses').delete().eq('user_id', userId),
        // Delete payment methods
        this.supabase.from('payment_methods').delete().eq('user_id', userId),
        // Delete cart items
        this.supabase.from('cart_items').delete().eq('user_id', userId),
        // Note: We don't delete orders as they are important for business records
        // Instead, orders will remain but the user reference will be null
      ]);

      // Finally delete the user
      const { error } = await this.supabase
        .from('users')
        .delete()
        .eq('id', userId);

      if (error) throw error;

      return { message: 'User deleted successfully' };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async createUser(userData: {
    id: string;
    email: string;
    role: 'admin' | 'customer';
    firstName: string;
    lastName: string;
    phone?: string;
  }): Promise<ApiResponse<UserWithProfile>> {
    try {
      // Create user record
      const { data: user, error: userError } = await this.supabase
        .from('users')
        .insert({
          id: userData.id,
          email: userData.email,
          role: userData.role,
          email_verified: false,
        })
        .select()
        .single();

      if (userError) throw userError;

      // Create profile record
      const { data: profile, error: profileError } = await this.supabase
        .from('profiles')
        .insert({
          user_id: userData.id,
          first_name: userData.firstName,
          last_name: userData.lastName,
          phone: userData.phone,
        })
        .select()
        .single();

      if (profileError) throw profileError;

      return {
        data: {
          ...user,
          profile
        }
      };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Product operations
  async getProducts(params?: {
    categoryId?: string;
    status?: string;
    featured?: boolean;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<ProductWithCategory>> {
    try {
      let query = this.supabase
        .from('products')
        .select(`
          *,
          category:categories(*)
        `, { count: 'exact' });

      // Apply filters
      if (params?.categoryId) {
        query = query.eq('category_id', params.categoryId);
      }
      if (params?.status) {
        query = query.eq('status', params.status);
      }
      if (params?.featured !== undefined) {
        query = query.eq('featured', params.featured);
      }
      if (params?.search) {
        query = query.or(`name.ilike.%${params.search}%,description.ilike.%${params.search}%`);
      }

      // Apply pagination
      const page = params?.page || 1;
      const limit = params?.limit || 20;
      const from = (page - 1) * limit;
      const to = from + limit - 1;

      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        data: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      };
    } catch (error) {
      throw new Error((error as Error).message);
    }
  }

  async getProduct(productId: string): Promise<ApiResponse<ProductWithCategory>> {
    try {
      const { data, error } = await this.supabase
        .from('products')
        .select(`
          *,
          category:categories(*)
        `)
        .eq('id', productId)
        .single();

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async createProduct(productData: Omit<Product, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Product>> {
    try {
      const { data, error } = await this.supabase
        .from('products')
        .insert(productData)
        .select()
        .single();

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async updateProduct(productId: string, productData: Partial<Product>): Promise<ApiResponse<Product>> {
    try {
      const { data, error } = await this.supabase
        .from('products')
        .update(productData)
        .eq('id', productId)
        .select()
        .single();

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async deleteProduct(productId: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await this.supabase
        .from('products')
        .delete()
        .eq('id', productId);

      if (error) throw error;

      return { message: 'Product deleted successfully' };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Category operations
  async getCategories(): Promise<ApiResponse<Category[]>> {
    try {
      const { data, error } = await this.supabase
        .from('categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) throw error;

      return { data: data || [] };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async createCategory(categoryData: Omit<Category, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Category>> {
    try {
      const { data, error } = await this.supabase
        .from('categories')
        .insert(categoryData)
        .select()
        .single();

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Cart operations
  async getCartItems(userId: string): Promise<ApiResponse<CartItemWithProduct[]>> {
    try {
      const { data, error } = await this.supabase
        .from('cart_items')
        .select(`
          *,
          product:products(*)
        `)
        .eq('user_id', userId);

      if (error) throw error;

      return { data: data || [] };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async addToCart(userId: string, productId: string, quantity: number): Promise<ApiResponse<CartItem>> {
    try {
      const { data, error } = await this.supabase
        .from('cart_items')
        .upsert({
          user_id: userId,
          product_id: productId,
          quantity
        })
        .select()
        .single();

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async updateCartItem(cartItemId: string, quantity: number): Promise<ApiResponse<CartItem>> {
    try {
      const { data, error } = await this.supabase
        .from('cart_items')
        .update({ quantity })
        .eq('id', cartItemId)
        .select()
        .single();

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async removeFromCart(cartItemId: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await this.supabase
        .from('cart_items')
        .delete()
        .eq('id', cartItemId);

      if (error) throw error;

      return { message: 'Item removed from cart' };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async clearCart(userId: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await this.supabase
        .from('cart_items')
        .delete()
        .eq('user_id', userId);

      if (error) throw error;

      return { message: 'Cart cleared' };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Order operations
  async getOrders(params?: {
    userId?: string;
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<OrderWithItems>> {
    try {
      let query = this.supabase
        .from('orders')
        .select(`
          *,
          items:order_items(*),
          user:users(*)
        `, { count: 'exact' });

      // Apply filters
      if (params?.userId) {
        query = query.eq('user_id', params.userId);
      }
      if (params?.status) {
        query = query.eq('status', params.status);
      }

      // Apply pagination
      const page = params?.page || 1;
      const limit = params?.limit || 20;
      const from = (page - 1) * limit;
      const to = from + limit - 1;

      query = query.range(from, to).order('created_at', { ascending: false });

      const { data, error, count } = await query;

      if (error) throw error;

      return {
        data: data || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      };
    } catch (error) {
      throw new Error((error as Error).message);
    }
  }

  async getOrder(orderId: string): Promise<ApiResponse<OrderWithItems>> {
    try {
      const { data, error } = await this.supabase
        .from('orders')
        .select(`
          *,
          items:order_items(*),
          user:users(*)
        `)
        .eq('id', orderId)
        .single();

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async createOrder(orderData: {
    userId: string;
    userEmail: string;
    shippingAddress: any;
    billingAddress: any;
    paymentMethod: any;
    cartItems: { product_id: string; quantity: number }[];
  }): Promise<ApiResponse<string>> {
    try {
      const { data, error } = await this.supabase.rpc('create_order_with_items', {
        user_uuid: orderData.userId,
        user_email: orderData.userEmail,
        shipping_address: orderData.shippingAddress,
        billing_address: orderData.billingAddress,
        payment_method: orderData.paymentMethod,
        cart_items: orderData.cartItems
      });

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async updateOrderStatus(
    orderId: string, 
    status: string, 
    adminNotes?: string, 
    trackingNumber?: string, 
    trackingUrl?: string
  ): Promise<ApiResponse<boolean>> {
    try {
      const { data, error } = await this.supabase.rpc('update_order_status', {
        order_uuid: orderId,
        new_status: status,
        admin_notes: adminNotes,
        tracking_number: trackingNumber,
        tracking_url: trackingUrl
      });

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async deleteOrder(orderId: string): Promise<ApiResponse<void>> {
    try {
      // First delete order items (foreign key constraint)
      const { error: itemsError } = await this.supabase
        .from('order_items')
        .delete()
        .eq('order_id', orderId);

      if (itemsError) {
        console.warn('Warning deleting order items:', itemsError.message);
      }

      // Then delete the order
      const { error } = await this.supabase
        .from('orders')
        .delete()
        .eq('id', orderId);

      if (error) throw error;

      return { message: 'Order deleted successfully' };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Address operations
  async getUserAddresses(userId: string): Promise<ApiResponse<Address[]>> {
    try {
      const { data, error } = await this.supabase
        .from('addresses')
        .select('*')
        .eq('user_id', userId)
        .order('is_default', { ascending: false });

      if (error) throw error;

      return { data: data || [] };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async createAddress(addressData: Omit<Address, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Address>> {
    try {
      const { data, error } = await this.supabase
        .from('addresses')
        .insert(addressData)
        .select()
        .single();

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async updateAddress(addressId: string, addressData: Partial<Address>): Promise<ApiResponse<Address>> {
    try {
      const { data, error } = await this.supabase
        .from('addresses')
        .update(addressData)
        .eq('id', addressId)
        .select()
        .single();

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async deleteAddress(addressId: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await this.supabase
        .from('addresses')
        .delete()
        .eq('id', addressId);

      if (error) throw error;

      return { message: 'Address deleted successfully' };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Payment method operations
  async getUserPaymentMethods(userId: string): Promise<ApiResponse<PaymentMethod[]>> {
    try {
      const { data, error } = await this.supabase
        .from('payment_methods')
        .select('*')
        .eq('user_id', userId)
        .order('is_default', { ascending: false });

      if (error) throw error;

      return { data: data || [] };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async createPaymentMethod(paymentMethodData: Omit<PaymentMethod, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<PaymentMethod>> {
    try {
      const { data, error } = await this.supabase
        .from('payment_methods')
        .insert(paymentMethodData)
        .select()
        .single();

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async deletePaymentMethod(paymentMethodId: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await this.supabase
        .from('payment_methods')
        .delete()
        .eq('id', paymentMethodId);

      if (error) throw error;

      return { message: 'Payment method deleted successfully' };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Notification operations
  async getUserNotifications(userId: string, unreadOnly = false): Promise<ApiResponse<Notification[]>> {
    try {
      let query = this.supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId);

      if (unreadOnly) {
        query = query.eq('read', false);
      }

      query = query.order('created_at', { ascending: false });

      const { data, error } = await query;

      if (error) throw error;

      return { data: data || [] };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async markNotificationAsRead(notificationId: string): Promise<ApiResponse<Notification>> {
    try {
      const { data, error } = await this.supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId)
        .select()
        .single();

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Analytics operations
  async getSalesAnalytics(startDate?: string, endDate?: string): Promise<ApiResponse<SalesAnalytics>> {
    try {
      const { data, error } = await this.supabase.rpc('get_sales_analytics', {
        start_date: startDate,
        end_date: endDate
      });

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  async getLowStockProducts(threshold = 10): Promise<ApiResponse<LowStockProduct[]>> {
    try {
      const { data, error } = await this.supabase.rpc('get_low_stock_products', {
        threshold
      });

      if (error) throw error;

      return { data: data || [] };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }

  // Inventory operations
  async updateProductInventory(
    productId: string,
    quantityChange: number,
    transactionType: string,
    referenceId?: string,
    referenceType?: string,
    notes?: string,
    userId?: string
  ): Promise<ApiResponse<boolean>> {
    try {
      const { data, error } = await this.supabase.rpc('update_product_inventory', {
        product_uuid: productId,
        quantity_change: quantityChange,
        transaction_type: transactionType,
        reference_uuid: referenceId,
        reference_type: referenceType,
        notes,
        user_uuid: userId
      });

      if (error) throw error;

      return { data };
    } catch (error) {
      return { error: (error as Error).message };
    }
  }
}

// Export singleton instance
export const db = new DatabaseService();