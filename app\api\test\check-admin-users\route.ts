import { NextRequest, NextResponse } from 'next/server';
import { createServiceClient } from '@/lib/supabase/service';

export async function GET(request: NextRequest) {
  try {
    const supabase = createServiceClient();
    
    console.log('Checking admin users in database...');
    
    // Check all users in database
    const { data: allUsers, error: allUsersError } = await supabase
      .from('users')
      .select('id, email, role, email_verified, created_at')
      .order('created_at', { ascending: false });
    
    console.log('All users result:', { allUsers, allUsersError });
    
    // Check specifically for admin users
    const { data: adminUsers, error: adminError } = await supabase
      .from('users')
      .select('id, email, role, email_verified')
      .eq('role', 'admin');
    
    console.log('Admin users result:', { adminUsers, adminError });
    
    return NextResponse.json({
      success: true,
      data: {
        totalUsers: allUsers?.length || 0,
        allUsers: allUsers || [],
        adminUsers: adminUsers || [],
        errors: {
          allUsersError: allUsersError?.message || null,
          adminError: adminError?.message || null
        }
      }
    });
    
  } catch (error) {
    console.error('Check admin users error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check admin users', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
