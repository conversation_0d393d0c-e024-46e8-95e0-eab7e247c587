// Test endpoint to verify products API without authentication
import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database/index';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing products API...');
    
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId') || undefined;
    const status = searchParams.get('status') || undefined;
    const featured = searchParams.get('featured') === 'true' ? true : undefined;
    const search = searchParams.get('search') || undefined;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    const result = await db.getProducts({
      categoryId,
      status,
      featured,
      search,
      page,
      limit
    });

    console.log('Products result:', result);

    return NextResponse.json({
      success: true,
      message: 'Products API test successful',
      result
    });
  } catch (error) {
    console.error('Test products API error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to test products API',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}