export interface Product {
  id: string
  name: string
  description: string
  price: number
  categoryId: string
  category: Category
  inventoryCount: number
  images: ProductImage[]
  status: 'active' | 'inactive' | 'out_of_stock'
  createdAt: Date
  updatedAt: Date
}

export interface ProductImage {
  id: string
  publicId: string
  secureUrl: string
  altText: string
  isPrimary: boolean
  order: number
}

export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  imageUrl?: string
  parentId?: string
  children?: Category[]
}