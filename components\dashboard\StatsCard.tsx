import { LucideIcon } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string;
  icon: LucideIcon;
  trend?: string;
  trendUp?: boolean;
}

export default function StatsCard({ 
  title, 
  value, 
  icon: Icon, 
  trend, 
  trendUp 
}: StatsCardProps) {
  return (
    <div className="bg-charcoal rounded-lg p-6 border border-gold/20">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-400">{title}</p>
          <p className="text-2xl font-bold text-white mt-2">{value}</p>
          {trend && (
            <p className={`text-sm mt-2 ${
              trendUp ? 'text-green-400' : 'text-yellow-400'
            }`}>
              {trend}
            </p>
          )}
        </div>
        <div className="p-3 bg-gold/10 rounded-full">
          <Icon className="w-6 h-6 text-gold" />
        </div>
      </div>
    </div>
  );
}