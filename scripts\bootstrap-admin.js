#!/usr/bin/env node

/**
 * Bootstrap Admin Script for JOOKA E-commerce Platform
 * This script creates the first admin user by temporarily bypassing RLS
 */

const { createClient } = require('@supabase/supabase-js');
const readline = require('readline');

// Try to load dotenv, but don't fail if it's not available
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key (bypasses RLS)
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
  db: {
    schema: 'public',
  },
});

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function bootstrapAdmin() {
  try {
    console.log('🚀 JOOKA E-commerce Bootstrap Admin\n');
    console.log('This script creates the first admin user by bypassing RLS policies.');
    console.log('Use this only for initial setup.\n');
    
    // Get the details
    const email = await question('Enter admin email: ');
    const userId = await question('Enter user ID (from Supabase Auth): ');
    const firstName = await question('Enter first name: ');
    const lastName = await question('Enter last name: ');
    
    rl.close();
    
    if (!email || !userId || !firstName) {
      throw new Error('Email, user ID, and first name are required');
    }
    
    console.log('\n🔄 Creating admin user with service role privileges...');
    
    // Use raw SQL to bypass RLS policies
    console.log('🔄 Creating user record...');
    
    const { data: userData, error: userError } = await supabase
      .rpc('exec_sql', {
        sql: `
          INSERT INTO public.users (id, email, email_verified, role, created_at, updated_at)
          VALUES ('${userId}', '${email}', true, 'admin', NOW(), NOW())
          ON CONFLICT (id) DO UPDATE SET
            role = 'admin',
            email_verified = true,
            updated_at = NOW()
          RETURNING *;
        `
      });
    
    if (userError) {
      console.error('User creation error:', userError);
      
      // Fallback: try direct insert with service role
      console.log('🔄 Trying direct insert...');
      
      const { error: directUserError } = await supabase
        .from('users')
        .upsert({
          id: userId,
          email: email,
          email_verified: true,
          role: 'admin',
        }, {
          onConflict: 'id'
        });
      
      if (directUserError) {
        throw new Error(`Failed to create user: ${directUserError.message}`);
      }
    }
    
    console.log('✅ User record created/updated');
    
    // Create profile
    console.log('🔄 Creating profile...');
    
    const { error: profileError } = await supabase
      .from('profiles')
      .upsert({
        user_id: userId,
        first_name: firstName,
        last_name: lastName || '',
      }, {
        onConflict: 'user_id'
      });
    
    if (profileError) {
      console.warn('⚠️  Profile creation warning:', profileError.message);
      console.log('✅ User created successfully, but profile may need manual creation');
    } else {
      console.log('✅ Profile created/updated');
    }
    
    // Verify the user was created
    console.log('🔍 Verifying user creation...');
    
    const { data: verifyUser, error: verifyError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (verifyError || !verifyUser) {
      throw new Error('User verification failed - user may not have been created');
    }
    
    console.log('\n🎉 Bootstrap completed successfully!');
    console.log(`📧 Email: ${email}`);
    console.log(`🆔 User ID: ${userId}`);
    console.log(`👤 Name: ${firstName} ${lastName || ''}`);
    console.log(`🔑 Role: ${verifyUser.role}`);
    console.log(`✅ Email Verified: ${verifyUser.email_verified}`);
    console.log('\n📋 Next steps:');
    console.log('1. Try signing in at /auth/signin');
    console.log('2. You should be redirected to /admin/dashboard');
    console.log('3. You can now create additional admin users through the UI');
    
  } catch (error) {
    console.error('\n❌ Bootstrap failed:', error.message);
    console.log('\n🔧 Manual fix instructions:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Run this SQL command:');
    console.log(`
INSERT INTO public.users (id, email, email_verified, role)
VALUES ('${process.argv[2] || 'YOUR_USER_ID'}', '${process.argv[3] || 'YOUR_EMAIL'}', true, 'admin')
ON CONFLICT (id) DO UPDATE SET role = 'admin';

INSERT INTO public.profiles (user_id, first_name, last_name)
VALUES ('${process.argv[2] || 'YOUR_USER_ID'}', '${process.argv[4] || 'FIRST_NAME'}', '${process.argv[5] || 'LAST_NAME'}')
ON CONFLICT (user_id) DO UPDATE SET 
  first_name = '${process.argv[4] || 'FIRST_NAME'}',
  last_name = '${process.argv[5] || 'LAST_NAME'}';
    `);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n👋 Bootstrap cancelled');
  rl.close();
  process.exit(0);
});

// Run the script
if (require.main === module) {
  bootstrapAdmin();
}

module.exports = { bootstrapAdmin };