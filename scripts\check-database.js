#!/usr/bin/env node

/**
 * Database Check Script - Verify database setup and connectivity
 */

const { createClient } = require('@supabase/supabase-js');

// Try to load dotenv
try {
  require('dotenv').config({ path: '.env.local' });
} catch (error) {
  console.log('⚠️  dotenv not found, using system environment variables');
}

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration. Please check your .env.local file.');
  console.error('Required variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function checkDatabase() {
  try {
    console.log('🔍 JOOKA E-commerce Database Check\n');
    console.log(`🌐 Supabase URL: ${supabaseUrl}`);
    console.log(`🔑 Service Key: ${supabaseServiceKey.substring(0, 20)}...`);
    console.log('');
    
    // Test basic connectivity
    console.log('🔄 Testing database connectivity...');
    const { data: testData, error: testError } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (testError) {
      console.error('❌ Database connectivity failed:', testError.message);
      return;
    }
    
    console.log('✅ Database connectivity successful');
    
    // Check if tables exist
    console.log('\n🔄 Checking required tables...');
    
    const tables = ['users', 'profiles', 'products', 'categories', 'orders'];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.error(`❌ Table '${table}' check failed:`, error.message);
        } else {
          console.log(`✅ Table '${table}' exists and accessible`);
        }
      } catch (err) {
        console.error(`❌ Table '${table}' check failed:`, err.message);
      }
    }
    
    // Check existing users
    console.log('\n🔄 Checking existing users...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, role, email_verified, created_at');
    
    if (usersError) {
      console.error('❌ Failed to fetch users:', usersError.message);
    } else {
      console.log(`✅ Found ${users.length} users in database:`);
      users.forEach(user => {
        console.log(`   - ${user.email} (${user.role}) - Verified: ${user.email_verified}`);
      });
    }
    
    // Check auth users
    console.log('\n🔄 Checking Supabase Auth users...');
    try {
      const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
      
      if (authError) {
        console.error('❌ Failed to fetch auth users:', authError.message);
      } else {
        console.log(`✅ Found ${authUsers.users.length} auth users:`);
        authUsers.users.forEach(user => {
          console.log(`   - ${user.email} - Confirmed: ${!!user.email_confirmed_at}`);
        });
      }
    } catch (err) {
      console.error('❌ Auth users check failed:', err.message);
    }
    
    // Check profiles
    console.log('\n🔄 Checking user profiles...');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('user_id, first_name, last_name');
    
    if (profilesError) {
      console.error('❌ Failed to fetch profiles:', profilesError.message);
    } else {
      console.log(`✅ Found ${profiles.length} user profiles`);
    }
    
    console.log('\n🎉 Database check completed!');
    
  } catch (error) {
    console.error('\n❌ Database check failed:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  checkDatabase();
}

module.exports = { checkDatabase };