import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Test basic connection by trying to fetch from a simple table
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('id, name')
      .limit(5);

    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, name')
      .limit(5);

    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email')
      .limit(5);

    return NextResponse.json({
      success: true,
      message: 'Database connection test',
      data: {
        categories: {
          count: categories?.length || 0,
          error: categoriesError?.message || null,
          data: categories || []
        },
        products: {
          count: products?.length || 0,
          error: productsError?.message || null,
          data: products || []
        },
        users: {
          count: users?.length || 0,
          error: usersError?.message || null,
          data: users || []
        }
      }
    });
  } catch (error) {
    console.error('Database connection test error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Database connection failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}